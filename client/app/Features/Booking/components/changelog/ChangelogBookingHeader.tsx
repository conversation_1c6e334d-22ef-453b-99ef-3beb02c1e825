import moment from 'moment';
import * as React from 'react';
import styled from 'styled-components';
import { Airport } from '../../../../models/Airport';
import { AccountSettings } from '../../../../reducers/CurrentAccount';
import { ICurrentUserState } from '../../../CurrentUser/Reducer';
import { Booking_checkinOut_checkinUsers } from '../../models/__generated__/Booking';
import { ChangelogAcknowledgement } from './ChangelogAcknowledgement';

enum dayColor {
  'ends-at-present' = '#2aa938',
  'ends-at-past' = '#b73131',
}
const StyledHeader = styled.div`
  font-weight: bold;
  float: left;
  display: flex;
  align-items: center;
`;

const FlexStyle = styled.div`
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-bottom: -15px;
  width: 100%;
`;

const AroundFlex = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: -12px;
  color: #999;
  @media (max-width: 500px) {
    justify-content: flex-end;
  }
`;

const StyledAge = styled.span`
  color: ${(props) =>
    props.type === 'meeting' ||
    props.type === 'maintenance' ||
    props.status === 'completed' ||
    props.status === 'cancelled'
      ? ''
      : dayColor[props.bookingAge]};
  };
`;

const StyledDaysExtra = styled.span`
  margin-left: 6px;
  bottom: 5px;
  position: relative;
  font-size: 10px;
  font-weight: bold;
  color: #ff4747;
`;

const StyledDay = styled(StyledAge)`
  font-weight: bold;
  float: right;
  white-space: nowrap;
  margin-left: 10px;
`;

const FlightStyle = styled.span`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 47px;
`;

const StyledTimeContainer = styled.div`
  clear: both;
`;

const StyledSlash = styled.span`
  white-space: pre-wrap;
`;

interface Revision {
  name?: string;
  id?: string;
  program?: Program;
}

interface Program {
  name?: string;
  id?: string;
}

interface Props {
  currentUser: ICurrentUserState
  settings: AccountSettings;
  startsAt: DateTime;
  endsAt: DateTime;
  flightStartsAt: DateTime;
  flightEndsAt: DateTime;
  header: string;
  subjectCategory: { name: string };
  departureAirport: Airport;
  arrivalAirport: Airport;
  type: string;
  bookingAge: string;
  status: string;
  activity?: {
    id?: string;
    name?: string;
  };
  checkinUsers?: Booking_checkinOut_checkinUsers[];
  frontPage?: boolean;
  programRevision?: Revision;
  approved?: boolean;
}

export const ChangelogBookingHeader = (props: Props) => {
  const spanningDays = moment(props.endsAt).diff(moment(props.startsAt), 'days');

  const renderFlightTime = () => {
    if (props.flightStartsAt && props.flightEndsAt)
      return (
        <span>
          <FlightStyle>{moment(props.flightStartsAt).format('LT') + ' - '}</FlightStyle>
          <FlightStyle>{moment(props.flightEndsAt).format('LT') + ', '}</FlightStyle>
        </span>
      );
  };

  const renderSubheaderText = () => {
    if (props.header === 'Extra theory') {
      return props.programRevision?.program.name;
    } else {
      return props.subjectCategory?.name || props.activity?.name;
    }
  };

  const renderHeader = () => {
    if ((props.header === 'Rental' || props.header === 'Single student') && props.approved) return `${props.header} - Approved`;
    if ((props.header === 'Rental' || props.header === 'Single student') && !props.approved) return `${props.header} - Awaiting approval`;

    return props.header;
  };

  return (
    <FlexStyle>
      <div>
        <StyledHeader>
          {renderHeader()}
          {props.frontPage && props.settings.use_acknowledgement_and_authorization && (
            <ChangelogAcknowledgement users={props.checkinUsers} currentUser={props.currentUser} />
          )}
        </StyledHeader>
        <span style={{ float: 'left', marginBottom: '25px', clear: 'both' }}>{renderSubheaderText()}</span>
      </div>

      <div>
        <StyledDay>
          {moment(props.startsAt).format(props.settings.moment_long)}
        </StyledDay>

        <StyledTimeContainer>
          <span style={{ float: 'right', display: 'flex' }}>
            <StyledAge {...props}>{moment(props.startsAt).format('LT') + ', '}</StyledAge>
            {renderFlightTime()}
            <StyledAge {...props}>{moment(props.endsAt).format('LT')}</StyledAge>

            {spanningDays > 0 && <StyledDaysExtra style={{ float: 'right' }}>+{spanningDays}</StyledDaysExtra>}
          </span>
        </StyledTimeContainer>

        {props.arrivalAirport && props.departureAirport && props.settings.enable_departure_arrival_booking && (
          <StyledTimeContainer>
            <AroundFlex>
              <span />
              <FlightStyle title={props.departureAirport.name}>{`${props.departureAirport.name}`}</FlightStyle>
              <StyledSlash>{' / '}</StyledSlash>
              <FlightStyle title={props.arrivalAirport.name}>{`${props.arrivalAirport.name}`}</FlightStyle>
              <span />
            </AroundFlex>
          </StyledTimeContainer>
        )}
      </div>
    </FlexStyle>
  );
};
