# frozen_string_literal: true

class Ability
  class FlightInstructorAccess < InstructorAccess
    def set_access
      return unless has_role? :flight_instructor

      super
      students_can_request_bookings_access
    end

    def students_can_request_bookings_access
      return unless account_setting.students_can_request_bookings?

      can(:approve_aircraft_rental, Booking)
      can(:approval_modal, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:approve_requested, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:reject_requested, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:approve, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
    end
  end
end
