# frozen_string_literal: true

class Ability
  class GroundInstructorAccess < InstructorAccess
    def set_access
      return unless has_role? :ground_instructor

      super

      # theory
      if account_setting.instructors_can_see_other_instructors_activities?
        # For public API scoping:
        can :read, :all_class_theories
        can :read, :all_exams
        can :read, :all_extra_theories
        can :read, :all_progress_tests
        can :read, :all_theory_releases
        can :read, :all_type_questionnaires
      end

      students_can_request_bookings_access
    end

    def students_can_request_bookings_access
      return unless account_setting.students_can_request_bookings?

      can(:approval_modal, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:approve_requested, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:reject_requested, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
      can(:approve, Booking, type_of_booking: 'lecture', user_resources: { user_id: user.id, role: %i[instructor] })
    end
  end
end
