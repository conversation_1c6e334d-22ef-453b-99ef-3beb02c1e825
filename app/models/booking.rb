# frozen_string_literal: true

class Booking < ApplicationRecord
  serialize :recurring, type: Hash, coder: YAM<PERSON>
  has_paper_trail
  SUBJECT_BOOKINGS = %w[theory_multiple progress_test theory_release theory_exam type_questionnaire].freeze
  THEORY_BOOKINGS = %w[theory meeting] + SUBJECT_BOOKINGS
  FLIGHT_BOOKINGS = %w[rental lecture activity crew_lecture].freeze
  AIRCRAFT_BOOKINGS = %w[maintenance] + FLIGHT_BOOKINGS
  TYPE_OF_BOOKINGS = THEORY_BOOKINGS + AIRCRAFT_BOOKINGS
  CLASS_BOOKINGS = %w[theory_multiple progress_test theory_release theory_exam type_questionnaire].freeze
  CLASSROOM_SUMMARY_BOOKINGS = SUBJECT_BOOKINGS + %w[theory]
  AIRCRAFT_SUMMARY_BOOKINGS = FLIGHT_BOOKINGS
  FRONTPAGE_BOOKING_DATE = '2018-03-01'
  NEUTRALIZE_STRING_ATTRIBUTES = %w[comment].freeze
  include ActionView::Helpers::UrlHelper
  include TimeUtil
  include Neutralization
  include Auditable
  include Trackable
  include BookingRecurrent
  include Booking<PERSON>ublish<PERSON>

  def self.sorter
    ::Sorters::Booking
  end

  neutralize_string_fields NEUTRALIZE_STRING_ATTRIBUTES

  belongs_to :account, optional: false
  belongs_to :plane, optional: true

  # Multiple theory booking
  belongs_to :program_revision, optional: true
  belongs_to :classroom, optional: true
  belongs_to :subject_category, optional: true
  belongs_to :team, optional: true
  belongs_to :activity, optional: true
  belongs_to :departure_airport, class_name: 'Airport', optional: true
  belongs_to :arrival_airport, class_name: 'Airport', optional: true
  has_one :theory_lecture
  has_one :program, through: :program_revision
  has_many :booking_documents, -> { Attachment.document }, class_name: 'Attachment', as: :attachable, dependent: :destroy, inverse_of: :attachable
  accepts_nested_attributes_for :booking_documents, reject_if: proc { |attrs| %w[file_url].all? { |a| attrs[a].blank? } }, allow_destroy: true
  has_many :booking_activities

  has_many :cancelled_bookings, dependent: :destroy

  has_many :booking_conflict_links, dependent: :destroy
  has_many :booking_conflicts, through: :booking_conflict_links, dependent: :destroy
  with_options through: :booking_conflict_links, source: :booking_conflict do |assoc|
    assoc.has_many :certificate_requirement_conflicts, -> { BookingConflict.certificate_requirement_conflicts }, class_name: 'CertificateRequirementConflict', dependent: :destroy
    assoc.has_many :ground_school_requirement_conflicts, -> { BookingConflict.ground_school_requirement_conflicts }, class_name: 'GroundSchoolRequirementConflict', dependent: :destroy
    assoc.has_many :flight_time_requirement_conflicts, -> { BookingConflict.flight_time_requirement_conflicts }, class_name: 'FlightTimeRequirementConflict', dependent: :destroy
    assoc.has_many :duty_time_requirement_conflicts, -> { BookingConflict.duty_time_requirement_conflicts }, class_name: 'DutyTimeRequirementConflict', dependent: :destroy
    assoc.has_many :user_balance_conflicts, -> { BookingConflict.user_balance_conflicts }, class_name: 'UserBalanceConflict', dependent: :destroy
    assoc.has_many :maintenance_requirement_conflicts, -> { BookingConflict.maintenance_requirement_conflicts }, class_name: 'MaintenanceRequirementConflict', dependent: :destroy
    assoc.has_many :squawk_warning_conflicts, -> { BookingConflict.squawk_warning_conflicts }, class_name: 'SquawkWarningConflict', dependent: :destroy
    assoc.has_many :resource_overbooking_conflicts, -> { BookingConflict.resource_overbooking_conflicts }, class_name: 'ResourceOverbookingConflict', dependent: :destroy
    assoc.has_many :availability_conflicts, -> { BookingConflict.availability_conflicts }, class_name: 'AvailabilityConflict', dependent: :destroy
  end

  has_many :user_resources, dependent: :destroy, as: :resource, inverse_of: :resource
  has_many :bookable_resources, dependent: :destroy, inverse_of: :booking
  has_many :flights, dependent: :nullify, inverse_of: :booking

  has_one :checkin_out, dependent: :destroy
  accepts_nested_attributes_for :checkin_out, reject_if: :missing_attributes
  with_options class_name: 'BookableResource', inverse_of: :booking do |assoc|
    assoc.has_many :booked_bookable_resources, -> { booked }
    assoc.has_many :completed_bookable_resources, -> { completed }
    assoc.has_many :customer_bookable_resources, -> { customer }
    assoc.has_many :user_lecture_bookable_resources, -> { user_lecture }
  end

  with_options source: :resource, source_type: 'UserLecture' do
    has_many :user_lectures, through: :user_lecture_bookable_resources
    has_many :booked_user_lectures, through: :booked_bookable_resources
    has_many :completed_user_lectures, through: :completed_bookable_resources
  end

  with_options source: :resource, source_type: 'Customer' do
    has_many :customers, through: :customer_bookable_resources
  end

  with_options through: :user_resources, source: :user do |assoc|
    assoc.has_many :instructors, -> { UserResource.instructor }
    assoc.has_many :students, -> { UserResource.student }
    assoc.has_many :renters, -> { UserResource.renter }
    assoc.has_many :crew, -> { UserResource.crew }
    assoc.has_many :pics, -> { UserResource.pic }
    assoc.has_many :participants, -> { UserResource.participant }
    assoc.has_many :expenses_users, -> { UserResource.expenses }
    assoc.has_many :observers, -> { UserResource.observer }
    assoc.has_many :instructor_pic_or_renter, -> { UserResource.instructor_pic_or_renter }
    assoc.has_many :users
  end

  with_options through: :user_resources, source: :user do |assoc|
    assoc.has_many :sorted_students, -> { UserResource.student.sorted }
  end

  accepts_nested_attributes_for :user_resources,
                                :booked_user_lectures,
                                :bookable_resources,
                                :customers,
                                :students,
                                :participants,
                                allow_destroy: true

  validates :user_resources, roles_with: { one: :renter, if: :rental? }
  validates :user_resources, roles_with: { one: %w[instructor student], many: :observer, if: :lecture? }
  validates :user_resources, roles_with: { one: :instructor, many: { observer: { min: 0 }, student: { min: 2, max: 2 } }, if: :crew_lecture? }
  validates :user_resources, roles_with: { one: :pic, many: :crew, if: :activity? }
  validates :user_resources, roles_with: { one: :instructor, many: { student: { min: 1 } }, if: :theory_multiple? }
  validates :user_resources, roles_with: { one: :instructor, many: { student: { min: 1 } }, if: :progress_test? }
  validates :user_resources, roles_with: { one: :instructor, many: { student: { min: 1 } }, if: :theory_release? }
  validates :user_resources, roles_with: { one: :instructor, many: { student: { min: 1 } }, if: :theory_exam? }
  validates :user_resources, roles_with: { one: :instructor, many: { student: { min: 1 } }, if: :type_questionnaire? }
  validates :user_resources, roles_with: { one: %w[instructor student], if: :extra_theory? }
  validates :user_resources, roles_with: { many: { participant: { min: 1 } }, if: :meeting? }
  validates :user_resources, absence: true, if: :maintenance?

  validates :starts_at, :ends_at, :type_of_booking, presence: true
  validates :starts_at, :ends_at, date: true

  validates :plane, current: :account, presence: { if: -> { AIRCRAFT_BOOKINGS.include? type_of_booking } }
  validates :classroom, current: :account, presence: { if: -> { THEORY_BOOKINGS.include? type_of_booking } }
  validates :subject_category, presence: { if: -> { SUBJECT_BOOKINGS.include? type_of_booking } }
  validates :theory_course, presence: { if: -> { SUBJECT_BOOKINGS.include? type_of_booking } }
  validates :activity, presence: { if: -> { activity? } }
  validates :status, inclusion: { in: %w[migrated open cancelled completed] }
  validates :type_of_booking, inclusion: { in: TYPE_OF_BOOKINGS }
  validates_with BookingValidator
  # Emails

  before_validation :set_flight_times
  before_validation :remove_ms_s
  after_create :send_booker_email, :send_lecture_request_email

  scope :sorted, -> { order(:starts_at) }
  scope :theory_multi, -> { where(type_of_booking: 'theory_multiple') }
  scope :progress_test, -> { where(type_of_booking: 'progress_test') }
  scope :theory_release, -> { where(type_of_booking: 'theory_release') }
  scope :theory_exam, -> { where(type_of_booking: 'theory_exam') }
  scope :class_theory, -> { where(type_of_booking: 'theory_multiple') }
  scope :extra_theory, -> { where(type_of_booking: 'theory') }
  scope :type_questionnaire, -> { where(type_of_booking: 'type_questionnaire') }
  scope :multi, -> { where(type_of_booking: 'crew_lecture') }
  scope :theory, -> { where(type_of_booking: THEORY_BOOKINGS) }
  scope :rentals, -> { where(type_of_booking: 'rental') }
  scope :activities, -> { where(type_of_booking: 'activity') }
  scope :maintenance_bookings, -> { where(type_of_booking: 'maintenance') }
  scope :single_student, -> { where(type_of_booking: 'lecture') }
  scope :multi_student, -> { where(type_of_booking: 'crew_lecture') }
  scope :meetings, -> { where(type_of_booking: 'meeting') }
  scope :not_meetings, -> { where.not(type_of_booking: 'meeting') }
  scope :lectures, -> { where(type_of_booking: %w[lecture crew_lecture]) }
  scope :not_theory, -> { where(type_of_booking: AIRCRAFT_BOOKINGS) }
  scope :non_maintenance_aircraft, -> { where(type_of_booking: FLIGHT_BOOKINGS) }
  scope :aircraft_summary, -> { where(type_of_booking: AIRCRAFT_SUMMARY_BOOKINGS) }
  scope :classroom_summary, -> { where(type_of_booking: CLASSROOM_SUMMARY_BOOKINGS) }
  scope :checkin_able, -> { where(type_of_booking: %w[rental lecture activity crew_lecture]) }
  scope :in_arrival, -> { joins(:checkin_out).where(checkin_outs: { current_stage: CheckinOut::ARRIVAL_TYPE }) }
  scope :overdue, ->(hours) do
    (
      if hours&.positive?
        where('ends_at >= ?', Time.zone.now - hours.hours)
      elsif hours&.zero?
        where('ends_at >= ?', Time.zone.now)
      else
        where('ends_at >= ?', FRONTPAGE_BOOKING_DATE)
      end)
  end
  scope :one_day_overdue, -> { where('ends_at >= ?', Time.zone.now - 48.hours) }
  scope :open_bookings, -> { where(status: 'open') }
  scope :not_cancelled, -> { where.not(status: 'cancelled') }
  scope :not_cancelled_for_user, ->(user) { not_cancelled.where('NOT EXISTS (SELECT * FROM cancelled_bookings WHERE cancelled_bookings.user_id = ? AND cancelled_bookings.booking_id = bookings.id)', user.id) }
  scope :cancelled, -> { where(status: 'cancelled') }
  scope :not_deleted, -> { joins("LEFT JOIN booking_activities ON booking_activities.booking_id = bookings.id AND booking_activities.bookable_type = 'CancelledBooking'") }
  scope :completed, -> { where(status: 'completed') }
  scope :with_plane, -> { includes(:plane).where('bookings.plane_id IS NOT NULL') }
  scope :with_room, -> { includes(:classroom).where('classroom_id IS NOT NULL') }
  scope :with_booking_activities, -> { includes(:booking_activities).where('booking_activities.booking_id = bookings.id') }
  scope :recent, -> { where('starts_at > ?', 1.month.ago) }
  scope :auto_completable, -> { where(type_of_booking: %w[meeting maintenance]) }
  scope :ongoing, -> { where('? BETWEEN starts_at AND ends_at', Time.zone.now) }
  scope :with_cancelled_bookings, -> { where(id: CancelledBooking.select(:booking_id).distinct) }
  scope :starts_in_the_future, -> { where('? < starts_at', Time.zone.now).order(:starts_at) }
  scope :with_user, ->(user) { where(UserResource.booking.where('user_resources.resource_id=bookings.id and user_id=?', user.try(:id) || user).arel.exists) }
  scope :not_in_past, -> { where('ends_at > ?', Time.zone.now) }
  scope :only_visible_bookings, ->(user) { where('starts_at <= ?', Time.current.beginning_of_day + user.booking_visibility_days.days) if user.booking_visibility_days.present? }

  TYPE_COLORS = {
    rental: '#FFA14D',
    lecture: '#4AADFF',
    theory: '#43D343',
    maintenance: '#A0A0A0',
    activity: '#C191EC',
    theory_multiple: '#43D343',
    progress_test: '#26A69A',
    theory_exam: '#26A69A',
    theory_release: '#26A69A',
    type_questionnaire: '#26A69A',
    crew_lecture: '#4AADFF',
    meeting: '#F3E640',
  }.freeze

  GRADIENT_COLORS = {
    rental: '#FFBF84',
    lecture: '#7AC3FF',
    theory: '#98E998',
    maintenance: '#A0A0A0',
    activity: '#D9B6F7',
    theory_multiple: '#98E998',
    progress_test: '#45CBBF',
    theory_exam: '#45CBBF',
    theory_release: '#45CBBF',
    type_questionnaire: '#45CBBF',
    crew_lecture: '#7AC3FF',
    meeting: '#F3E640',
  }.freeze

  STATUS_COLORS = {
    cancelled: 'rgba(232, 127, 127, 1)',
  }.freeze

  attr_accessor :skip_validations,
                :skip_availability_check,
                :similar_booking,
                :old_id

  def theory_course
    @theory_course ||= subject_category&.theory_course_id
  end

  attr_writer :theory_course

  def customer
    customers.first
  end

  def instructor
    if user_resources.loaded?
      (
        user_resources.first(&:instructor?) ||
          user_resources.first(&:pic?)
      )&.user
    else
      instructors.first || pics.first
    end
  end

  def instructor=(user)
    instructors << user
  end

  def user
    if user_resources.loaded?
      (
        user_resources.first(&:renter?) ||
          user_resources.first(&:student?)
      )&.user
    else
      renters.first || students.first
    end
  end

  def instructor_pic_or_renter
    if user_resources.loaded?
      (
        user_resources.first(&:instructor?) ||
          user_resources.first(&:pic?) ||
          user_resources.first(&:renter?)
      )&.user
    else
      instructors.first || pics.first || renters.first
    end
  end

  def similar_booking=(value)
    @similar_booking = value == 'true'
  end

  def similar_booking?
    @similar_booking
  end

  def self.types
    [
      [I18n.t('models.booking.lecture'), 'lecture'],
      [I18n.t('models.booking.crew_lecture'), 'crew_lecture'],
      [I18n.t('models.booking.rental'), 'rental'],
      [I18n.t('models.booking.activity'), 'activity'],
      [I18n.t('models.booking.maintenance'), 'maintenance'],
    ]
  end

  def self.theory_types
    [
      [I18n.t('models.booking.theory_multiple'), 'theory_multiple'],
      [I18n.t('models.booking.progress_test'), 'progress_test'],
      [I18n.t('models.booking.theory_release'), 'theory_release'],
      [I18n.t('models.booking.exam'), 'theory_exam'],
      [I18n.t('models.booking.meeting'), 'meeting'],
      [I18n.t('models.booking.theory'), 'theory'],
      [I18n.t('models.booking.type_questionnaire'), 'type_questionnaire'],
    ]
  end

  def self.combined_types
    types + theory_types
  end

  def self.types_hash
    {
      rental: I18n.t('models.booking.rental'),
      lecture: I18n.t('models.booking.lecture'),
      theory: I18n.t('models.booking.theory'),
      maintenance: I18n.t('models.booking.maintenance'),
      activity: I18n.t('models.booking.activity'),
      theory_multiple: I18n.t('models.booking.theory_multiple'),
      progress_test: I18n.t('models.booking.progress_test'),
      theory_release: I18n.t('models.booking.theory_release'),
      theory_exam: I18n.t('models.booking.theory_exam'),
      type_questionnaire: I18n.t('models.booking.type_questionnaire'),
      crew_lecture: I18n.t('models.booking.crew_lecture'),
      meeting: I18n.t('models.booking.meeting'),
    }
  end

  def self.involved_planes
    with_plane.map(&:plane).uniq
  end

  def self.involved_rooms
    with_room.map(&:classroom).uniq
  end

  def self.total_seconds
    current_scope.reorder(nil)
                 .select('SUM(EXTRACT(EPOCH FROM (COALESCE(flight_ends_at, ends_at) - COALESCE(flight_starts_at, starts_at)))) AS total_seconds')
                 .map(&:total_seconds).first&.seconds
  end

  def solo_lesson?
    return false unless lecture? || crew_lecture?
    return false unless bookable_resources&.any?

    @solo_lesson ||= bookable_resources.all? { |r| r.resource_type == 'UserLecture' && r.resource.try(:lecture).try(:only_solo?) }
  end

  def pob
    cu = user_resources
    cu = cu.reject(&:instructor?) if solo_lesson?
    cu.size
  end

  def self.user_resources
    UserResource.where(resource: current_scope)
  end

  def student_completed_or_cancelled?(user)
    student_cancelled?(user) || student_completed?(user)
  end

  def student_cancelled?(user)
    cancelled_bookings.where(user: user).any?
  end

  def student_completed?(user)
    completed_user_lectures.where(user: user).any?
  end

  def user_allocated?(user)
    user_resources.where(user: user).any?
  end

  def class_type?
    THEORY_BOOKINGS.include? type_of_booking
  end

  def aircraft_type?
    AIRCRAFT_BOOKINGS.include? type_of_booking
  end

  def fronted_resource_type
    return 'aircraft' if aircraft_type?

    'classrooms' if class_type?
  end

  def crew_lecture?
    type_of_booking == 'crew_lecture'
  end

  def lecture?
    type_of_booking == 'lecture'
  end

  def rental?
    type_of_booking == 'rental'
  end

  def theory?
    ['theory_multiple', 'theory'].include? type_of_booking
  end

  def extra_theory?
    type_of_booking == 'theory'
  end

  def theory_multiple?
    type_of_booking == 'theory_multiple'
  end

  def progress_test?
    type_of_booking == 'progress_test'
  end

  def theory_release?
    type_of_booking == 'theory_release'
  end

  def theory_exam?
    type_of_booking == 'theory_exam'
  end

  def type_questionnaire?
    type_of_booking == 'type_questionnaire'
  end

  def class_booking?
    CLASS_BOOKINGS.include? type_of_booking
  end

  def activity?
    type_of_booking == 'activity'
  end

  def maintenance?
    type_of_booking == 'maintenance'
  end

  def meeting?
    type_of_booking == 'meeting'
  end

  def header
    if rental? || lecture?
      return "#{Booking.types_hash[type_of_booking.to_sym]} - #{approved ? I18n.t('models.booking.approved') : I18n.t('models.booking.not_approved')}"
    end

    Booking.types_hash[type_of_booking.to_sym]
  end

  def resource_id
    return "plane_#{plane_id}" if plane_id

    "classroom_#{classroom_id}" if classroom_id
  end

  def title
    Bookings::TitlePresenter.new(self, nil).build
  end

  def start_c(current_date)
    if current_date.day == starts_at.day
      starts_at.hour + starts_at.min / 60.0
    else
      0
    end
  end

  def end_c(current_date)
    if current_date.day == ends_at.day
      24 - (ends_at.hour + ends_at.min / 60.0)
    else
      0
    end
  end

  def cancel_email_receivers
    pics + instructors + crew + observers
  end

  def email_recipients
    users
  end

  def error_on_other_stuff?
    errors.attribute_names.exclude?(:base) && errors.attribute_names.any?
  end

  def cancel
    return unless status != 'cancelled'

    update_attribute(:status, 'cancelled')
    send_booking_cancel_email
  end

  def cancelled?
    status == 'cancelled'
  end

  def complete
    update_attribute(:status, 'completed')
  end

  def completed?
    status == 'completed'
  end

  def open?
    status == 'open'
  end

  def open
    update_attribute(:status, 'open')
  end

  def seconds
    ends_at - starts_at
  end

  # This method marks all the booking-documents for reuse.
  # When a document is marked for reuse, the file is not deleted
  # from the "temp" folder uploads/*
  def process_reuse_documents
    booking_documents.each do |bd|
      bd.skip_clean = true
    end
  end

  def time_zone
    Time.zone.tzinfo.name
  end

  def end_time
    ends_at
  end

  def can_destroy?
    open? && booking_activities.none?
  end

  def notify_users
    return unless email_notice_on_change?
    return if recurrent_child?
    return if cancelled? || cancelled_bookings.any?

    email_recipients.each do |recipient|
      if recurrent_parent?
        BookingsRecurringMailer.notify_users(id, recipient.id).deliver_later
      elsif recipient == instructor && !approved? && account.account_setting.students_can_request_bookings?
        BookingsMailer.notify_lecture_request_new(self)
      else
        BookingsMailer.notify_users(id, recipient.id).deliver_later
      end
    end
  end

  def user_resource_for(user)
    user_resources.find { |ur| ur.user_id == user.id }
  end

  def notify_user?(user, override_email_notice_on_change: false)
    user_resource = user_resource_for(user)
    return false if !override_email_notice_on_change && !email_notice_on_change?
    return true if user_resource.blank?
    return true if user_resource.booking_email_sent? # If they have been in the loop they stay in the loop
    return true if user.booking_visibility_days.blank?

    starts_at <= Time.current.beginning_of_day + user.booking_visibility_days.days
  end

  def user_notified(user)
    user_resource_for(user)&.update(booking_email_sent: true)
  end

  def duration_seconds_for_period(start_date, end_date = date)
    theory_booking = class_type?
    type_ends_at = theory_booking ? ends_at : flight_ends_at
    type_starts_at = theory_booking ? starts_at : flight_starts_at

    clamp_params = [start_date.beginning_of_day.to_i, end_date.end_of_day.to_i]

    clamped_ends_at = type_ends_at.to_i.clamp(*clamp_params)
    clamped_start_at = type_starts_at.to_i.clamp(*clamp_params)

    clamped_ends_at - clamped_start_at
  end

  private

  def set_flight_times
    return unless %w[rental maintenance].include?(type_of_booking)

    self.flight_starts_at = starts_at
    self.flight_ends_at = ends_at
  end

  def remove_ms_s
    self.ends_at = ends_at&.change(usec: 0, sec: 0)
    self.starts_at = starts_at&.change(usec: 0, sec: 0)
    return unless FLIGHT_BOOKINGS.include? type_of_booking

    self.flight_starts_at = flight_starts_at&.change(usec: 0, sec: 0)
    self.flight_ends_at = flight_ends_at&.change(usec: 0, sec: 0)
  end

  def send_booker_email
    return true if recurrent_child?
    return unless rental? && !approved

    account.users.admin_rentals.active.each do |booker|
      if recurrent_parent?
        BookingsRecurringMailer.notify_booker(booker.id, id).deliver_later
      else
        BookingsMailer.notify_booker(booker.id, id).deliver_later
      end
    end
  end

  def send_lecture_request_email
    return if approved?
    return unless lecture?
    return unless account.account_setting.students_can_request_bookings?

    BookingsMailer.notify_lecture_request_new(self).deliver_later
  end

  def send_booking_cancel_email
    return true unless email_notice_on_change?
    return true if recurrent_child?
    return true if rental?

    cancel_email_receivers.each do |recipient|
      BookingsMailer.notify_about_cancel(id, recipient.id).deliver_later
    end
  end
end
