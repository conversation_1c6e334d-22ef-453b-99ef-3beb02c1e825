# frozen_string_literal: true

class PublicActivityBookingPresenter < BasePresenter
  def format_whodunnit
    current_account.users.find_by(id: model[:user_id])&.full_name || determine_autocompletion
  end

  def determine_autocompletion
    if %w[maintenance meeting].include?(model.object['type_of_booking']) && model.object['status'] == 'completed'
      I18n.t('.changelogs.bookings.presenter.autocompleted')
    else
      I18n.t('.changelogs.bookings.presenter.unknown_user')
    end
  end

  def format_action
    action = model[:key].to_s.split('.').last
    I18n.t(".changelogs.bookings.presenter.#{action}").upcase
  end

  def format_update_time
    I18n.l(model[:created_at], format: :long)
  end

  def booking
    return OpenStruct.new(model.object) if model.object

    model.trackable
  end

  def enhance
    merge_header
  end

  def booking_name
    return 'Unable to create booking name' if model.nil? || booking.starts_at.nil? || booking.ends_at.nil? || header.nil?

    starts_at = booking.flight_starts_at || booking.starts_at
    ends_at = booking.flight_ends_at || booking.ends_at

    "#{header} (#{I18n.l(starts_at.to_time, format: :short)} - #{I18n.l(ends_at.to_time, format: :short)})"
  end

  private

  def merge_header
    return if model.object.nil?

    model.object = model.object.merge({ header: header })
  end

  def header
    return unless booking&.type_of_booking

    booking_type = Booking.types_hash[booking.type_of_booking.to_sym]
    return booking_type unless booking.rental? || booking.lecture?

    "#{booking_type} - #{booking.approved ? I18n.t('models.booking.approved') : I18n.t('models.booking.not_approved')}"
  end
end
