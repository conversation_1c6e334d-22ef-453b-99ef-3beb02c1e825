# frozen_string_literal: true

require 'chroma'

class Bookings::ShowPresenter
  attr_reader :booking, :h

  delegate :tag, :safe_join, :t, :l, :controller, to: :h
  delegate :current_user, :current_account, to: :controller

  def initialize(booking, view_context)
    @booking = booking
    @h = view_context
  end

  def plane_info_row
    info_row(%w[fas fa-plane]) do
      [
        maintenance_warning_icon,
        squawk_warning_icon,
        h.link_to(booking.plane.callsign, booking.plane),
      ]
    end
  end

  def instructor_info_row
    instructor = booking.instructor

    info_row(%w[fas fa-person-chalkboard]) do
      [
        certificate_warning_icon(instructor),
        sign_off_warning_icon(instructor),
        availability_warning_icon(instructor),
        ftlm_time_warning_icon(instructor),
        h.link_to(instructor.to_s, instructor),
      ]
    end
  end

  def student_info_row
    student = booking.students.first

    info_row(%w[fas fa-user-graduate]) do
      [
        certificate_warning_icon(student),
        sign_off_warning_icon(student),
        availability_warning_icon(student),
        ftlm_time_warning_icon(student),
        ground_training_warning_icon(student),
        accounting_warning_icon(student),
        h.link_to(student.to_s, student),
      ]
    end
  end

  def lesson_info_row
    return if booking.bookable_resources&.first.blank?

    resource = booking.bookable_resources.first.resource
    title = resource.custom? ? "Extra lesson - #{resource.title}" : resource.title

    info_row(%w[far fa-rectangle-list]) do
      h.link_to(
        title,
        h.user_user_program_user_lecture_url(
          booking.students.first,
          booking.bookable_resources.first.resource.user_program,
          booking.bookable_resources.first.resource,
        ),
      )
    end
  end

  def observer_info_rows
    safe_join(
      booking.observers.map do |observer|
        info_row(%w[fas fa-user-secret]) do
          [
            h.link_to(observer.to_s, observer),
          ]
        end
      end,
    )
  end

  def notify_info_row
    info_row(%w[fas fa-envelope]) do
      [
        t('bookings.show.notify_via_email'),
        tag.span(class: 'text-muted', style: 'margin-left: 0.5rem;') do
          @booking.email_notice_on_change? ? t('bookings.show.enabled') : t('bookings.show.disabled')
        end,
      ]
    end
  end

  def booking_header_info
    color_class = if booking.ends_at < DateTime.current
                    'text-danger'
                  elsif booking.ends_at.today?
                    'text-success'
                  end

    safe_join(
      [
        tag.div(class: ['text-right', color_class], style: 'font-weight: bold;') do
          "#{booking.starts_at.strftime('%A')}, #{l(booking.starts_at.to_date)}"
        end,
        tag.div(class: 'text-right') do
          timestamp_info(color_class)
        end,
        tag.div(class: 'text-right text-muted') do
          "#{booking.departure_airport}, #{booking.arrival_airport}"
        end,
      ],
    )
  end

  def lecture_approval_actions
    return if booking.approved?
    return unless current_user.can?(:approve, booking)

    tag.div(class: 'row', style: 'margin-top: 10px;') do
      tag.div(class: 'col-md-12') do
        h.form_tag('', method: :post, data: { turbo: true }) do
          safe_join(
            [
              tag.div(class: 'form-group') do
                safe_join(
                  [
                    h.label_tag(:reason, 'Reason'),
                    h.text_area_tag(:reason, nil, class: 'form-control', rows: 3, style: 'resize: vertical; width: 100%; max-width: 100%;'),
                  ],
                )
              end,
              safe_join(
                [
                  h.button_tag(I18n.t('models.booking.button.decline_student_request'), type: 'submit', class: 'btn btn-danger', formmethod: 'post', formaction: h.reject_requested_booking_path(booking), data: { controller: 'freeze-loading-buttons', action: 'click->freeze-loading-buttons#click', disable_with: '<i class="fas fa-spinner fa-spin" style="margin-right: 2px;"></i> Declining...' }),
                  h.button_tag(I18n.t('models.booking.button.approve_student_request'), type: 'submit', class: 'btn btn-success', style: 'margin-left: 0.5rem;', formmethod: 'post', formaction: h.approve_requested_booking_path(booking), data: { controller: 'freeze-loading-buttons', action: 'click->freeze-loading-buttons#click', disable_with: '<i class="fas fa-spinner fa-spin" style="margin-right: 2px;"></i> Approving...' }),
                ],
              ),
            ],
          )
        end
      end
    end
  end

  def lecture_cancel_action
    return unless booking.approved?
    return if booking.cancelled_bookings.any?
    return unless current_user.can?(:create, CancelledBooking.new(booking: booking))

    tag.div(class: 'row', style: 'margin-top: 10px;') do
      tag.div(class: 'col-md-12') do
        h.link_to('Cancel', h.new_cancelled_booking_path(booking_id: booking.id, user_id: current_user.id), class: 'btn btn-danger')
      end
    end
  end

  def lecture_delete_action
    return if booking.approved?
    return unless current_user.can?(:destroy, booking)
    return unless booking.students.first == current_user

    tag.div(class: 'row', style: 'margin-top: 10px;') do
      tag.div(class: 'col-md-12') do
        h.button_to('Delete', h.booking_path(booking),
                    method: :delete,
                    class: 'btn btn-danger',
                    data: {
                      controller: 'freeze-loading-buttons',
                      action: 'click->freeze-loading-buttons#click',
                      disable_with: '<i class="fas fa-spinner fa-spin" style="margin-right: 2px;"></i> Deleting...',
                    })
      end
    end
  end

  def audit_info
    return unless current_user.has_role?(:administrator) || current_user.can?(:manage, :bookings)

    last_update = booking.public_activities.last
    last_updated_at = l(last_update.updated_at)
    last_updated_by = last_update.user.call_sign

    tag.div(class: 'row') do
      tag.div(class: 'col-md-12 text-right') do
        tag.small(title: t('bookings.show.last_updated', updated_at: last_updated_at, updated_by: last_updated_by)) do
          [
            tag.i(class: 'fas fa-bookmark', style: 'margin-right: 0.5rem;'),
            h.link_to(last_updated_at, h.changelogs_booking_path(booking)),
          ]
        end
      end
    end
  end

  private

  def timestamp_info(color_class)
    info = []
    info.push tag.span(class: color_class) { booking.starts_at.strftime('%H:%M') }
    info.push(tag.span { ",#{booking.flight_starts_at.strftime('%H:%M')}" }) if booking.flight_starts_at.present?
    info.push tag.span(style: 'margin-right: 0.5rem; margin-left: 0.5rem;') { '-' }
    info.push(tag.span { "#{booking.flight_ends_at.strftime('%H:%M')}," }) if booking.flight_ends_at.present?
    info.push tag.span(class: color_class) { booking.ends_at.strftime('%H:%M') }

    safe_join info
  end

  def info_row(icon_classes)
    tag.tr do
      safe_join [
        tag.td { tag.i(class: ['fa-fw', *icon_classes], style: 'margin-right: 1rem;') },
        tag.td { safe_join [yield] },
      ]
    end
  end

  def maintenance_warning_icon
    worst_warning = booking.plane.worst_maintenance_warning
    warning_icon(%w[fas fa-wrench], worst_warning&.color || '#00cc00', saturate: worst_warning&.color.present?, title: t('bookings.show.aircraft_maintenance_warning'))
  end

  def squawk_warning_icon
    worst_warning = booking.plane.worst_squawk_warning
    warning_icon(%w[fas fa-tire-flat], worst_warning&.color || '#00cc00', saturate: worst_warning&.color.present?, title: t('bookings.show.aircraft_squawk_warning'))
  end

  def certificate_warning_icon(user)
    worst_warning = user.worst_certificate_warning
    warning_icon(%w[far fa-copy], worst_warning&.color || '#00cc00', saturate: worst_warning&.color.present?, title: t('bookings.show.user_certificate_warning'))
  end

  def sign_off_warning_icon(user)
    warning_icon(%w[far fa-envelope], user.need_sign_off? ? '#FF8E8E' : '#00cc00', title: t('bookings.show.user_sign_off_warning'))
  end

  def ftlm_time_warning_icon(user)
    return unless current_account.flight_time_limitation_enabled?

    worst_warning = user.worst_ftlm_warning
    warning_icon(%w[far fa-clock-rotate-left], worst_warning&.color || '#00cc00', saturate: worst_warning&.color.present?, title: t('bookings.show.user_duty_time_warning'))
  end

  def availability_warning_icon(user)
    return unless current_account.availabilities_enabled?(user)

    warning_icon(%w[far fa-calendar-check], user.available_in?(booking.starts_at, booking.ends_at) ? '#00cc00' : '#FF8E8E', title: t('bookings.show.user_availability_warning'))
  end

  def ground_training_warning_icon(user)
    return unless UserPolicy.new(current_user, current_user).can_see_ground_school_warnings?

    worst_warning = user.worst_ground_school_warning
    warning_icon(%w[far fa-chalkboard-user], worst_warning&.color || '#00cc00', saturate: worst_warning&.color.present?, title: t('bookings.show.user_ground_training_warning'))
  end

  def accounting_warning_icon(user)
    return unless current_account.user_accounting?

    color = OrderItemPresenter.new(nil, nil).user_balance_status_color(user, booking.starts_at)
    warning_icon(%w[fas fa-dollar-sign], color, title: t('bookings.show.user_accounting_warning'))
  end

  def warning_icon(icon_classes, color, title:, saturate: false)
    color = saturate ? Chroma::Color.new(color).saturate(89).darken(46) : color

    tag.i(class: ['fa-fw', *icon_classes], color: color, title: title)
  end
end
