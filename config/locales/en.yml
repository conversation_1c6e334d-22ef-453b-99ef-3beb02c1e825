en:
  aircraft_remarks:
    flash_created: Remarks created
    form:
      new_title: New aircraft remark
      update_title: Update aircraft remark
      new_submit: Create remark
      update_submit: Update remark
  booking_conflicts:
    booking_conflicts: Booking conflicts <small>(%{date_text})</small>
    ignored_booking_conflicts: Ignored booking conflicts <small>(%{date_text})</small>
    bookings_button_hover: Open conflict center
    subtitle:
      active: Active conflicts
      ignored: Ignored conflicts
    page_number: Page %{page} of %{total}
    no_conflicts: There are no conflicts to display
    no_ignored_conflicts: There are no ignored conflicts to display
    show_active: Show all active conflicts
    show_ignored: Show all ignored conflicts
    change_conflict_preferences: Change conflict preferences
    no_hidden_conflicts: No hidden conflicts
    ignore_conflict: Ignore conflict
    ignored_by: Ignored by
    activate_conflict: Activate conflict
    actions: Actions
    tabs:
      all: All
      certificate_requirement_conflict: Certificate requirement
      maintenance_requirement_conflict: Maintenance requirement
      resource_overbooking_conflict: Resource overbooking
      user_balance_conflict: User balance
      squawk_warning_conflict: Discrepancy warning
      ground_school_requirement_conflict: Ground school requirement
      availability_conflict: Availability conflict
      flight_and_duty_time_requirement_conflict: Flight and duty time requirement
    types:
      certificate_requirement_conflict: Certificate requirement
      maintenance_requirement_conflict: Maintenance requirement
      resource_overbooking_conflict: Resource overbooking
      user_balance_conflict: User balance
      squawk_warning_conflict: Discrepancy warning
      availability_conflict: Availability conflict
      ground_school_requirement_conflict: Ground school requirement
      flight_time_requirement_conflict: Flight and duty time requirement
      duty_time_requirement_conflict: Flight and duty time requirement
    descriptions:
      certificate_requirement_conflict: "%{entity} has a missing or expired certificate"
      maintenance_requirement_conflict: "%{entity} has a missing or expired maintenance requirement"
      resource_overbooking_conflict: "%{entity} is double booked"
      user_balance_conflict: "%{entity} has a negative credit balance"
      squawk_warning_conflict: "%{entity} has an unresolved discrepancy warning"
      availability_conflict: "%{entity} is unavailable"
      ground_school_requirement_conflict: "%{entity} has exceeded a ground training requirement"
      flight_time_requirement_conflict: "%{entity} has exceeded a flight or duty time requirement"
      duty_time_requirement_conflict: "%{entity} has exceeded a flight or duty time requirement"
  flight_registration:
    log_type:
      airswitch: AIRSWITCH
      hobbs: HOBBS
      tach: TACH
      vdo: VDO
      datcon: DATCON
      edu: EDU
      flight_time: FLIGHT TIME
      block: BLOCK
      airborne: AIRBORNE
      heater_hobbs: HEATER HOBBS
      airswitch_start: "AIRSWITCH START"
      hobbs_start: "HOBBS START"
      tach_start: "TACH START"
      vdo_start: "VDO START"
      datcon_start: "DATCON START"
      edu_start: "EDU START"
      flight_time_start: "FLIGHT START"
      block_start: "OFF BLOCK"
      airborne_start: "TAKEOFF"
      heater_hobbs_start: "HEATER HOBBS START"
      airswitch_end: "AIRSWITCH END"
      hobbs_end: "HOBBS END"
      heater_hobbs_end: "HEATER HOBBS END"
      tach_end: "TACH END"
      vdo_end: "VDO END"
      datcon_end: "DATCON END"
      edu_end: "EDU END"
      flight_time_end: "FLIGHT END"
      block_end: "ON BLOCK"
      airborne_end: "LANDING"
  common:
    info:
      external_reference: The 'External reference' can be used to reference this specific %{type} externally (i.e. in other sources/systems)
  automatic_export_credential_errors:
    onedrive:
      access_denied: The OneDrive integration can only function if the requested permissions are granted.
      consent_required: The OneDrive integration can only function if the requested permissions are granted.
      unknown: An unknown error occured when attempting to integrate with OneDrive. Please ensure that you are using the appropriate account type (business/personal) with the integration in question, and try again later.
  flight_time_limitation_requirements:
    title: Flight time
    entry_description: Additional Warnings
    triggers: triggers
    warning: warning
    navigate_type: Go to flight time requirements
    trigger: trigger
    type: Requirement
    expiry_warning: expiry warning
    expired: expired
    form:
      than: than
      within: within
      triggers: triggers
    hours: hours
  duty_time_limitation_requirements:
    title: Duty time
    triggers: triggers
    warning: warning
    hours: hours
    trigger: trigger
    entry_description: Additional Warnings
    type: Requirement
    expiry_warning: expiry warning
    expired: expired
    form:
      duty_hours_within: duty hours within
      triggers: triggers
    navigate_type: Go to duty time requirements
  certificate_requirements:
    configuration: requirements
    administrator: Administrator
    aircraft: Aircraft
    crew: Crew
    type: Requirement
    flight_instructor: Flight Instructor
    ground_instructor: Ground Instructor
    renter: Renter
    staff: Staff
    student: Student
    notify_via_email: Notify via email
    notify_via_email_tooltip: If checked, daily emails will be sent when date warnings are active.
    post_text: days
    title: Certificates
    missing: Missing
    expired: Expired
    to_approval: Requiring approval
    defaults_description: 'A colored warning will appear when a certificate is:'
    certificate_types: 'Certificate requirements'
    duty_time_types: 'Duty time requirements'
    flight_time: 'Flight time requirements'
    entry_description: 'Date warnings'
    template_defaults:
      missing_template: 'Missing'
      to_approval_template: '{% if expiration_date == blank %}Requiring approval{% else %}Requiring approval - Expiry date: {{expiration_date}} {% endif %}'
      expired_template: 'Expired: {{expiration_date}}'
    add: Add requirement
    navigate_type: Go to certificate requirements
  certificate_warnings:
    requirers:
      user: User requirement
      administrator_configuration: Administrator requirement
      aircraft_configuration: Aircraft requirement
      crew_configuration: Crew requirement
      flight_instructor_configuration: Flight instructor requirement
      ground_instructor_configuration: Ground instructor requirement
      renter_configuration: Renter requirement
      staff_configuration: Staff requirement
      student_configuration: Student requirement
    reply_title: "Regarding %{certificate_type} %{template}"
  changelogs:
    bookings:
      bookings_index_container:
        header: List view/changelogs
        records: "Total bookings: %{hours} (%{count})"
        training: Training
        operation: Operation
        rental: Rental
        ground_training: Ground Training
        maintenance: Maintenance
        meeting: Meeting
        deleted: Deleted
      table_structure:
        please_wait: Please wait. Bookings are loading...
      tables:
        general: &general-table
          start: Start
          end: End
          type: Type
          status: Status
          aircraft: Aircraft
          records: "Total bookings: %{hours} (%{count})"
        training:
          <<: *general-table
          instructor: Instructor
          students: Student(s)
          lesson: Lesson
        operation:
          <<: *general-table
          pic: PIC
          customer: Customer
          operation: Operation
        rental:
          <<: *general-table
          renter: Renter
        ground_training:
          <<: *general-table
          classroom: Classroom
          class_student: Class/student
          instructor: Instructor
          program: Program
        maintenance:
          <<: *general-table
        meeting:
          <<: *general-table
          classroom: Classroom
        deleted:
          <<: *general-table
          aircraft_classroom: Aircraft/classroom
        no_bookings:
          no_booking_description: No bookings in the selected period
      show:
        booking_title: "Booking changelog"
        return: Return to bookings
      booking_details:
        cannot_show_changes: "Booking information missing"
      filter_report_section:
        find_report: Find report
        show_cancelled_bookings_visible: Cancelled bookings are visible
        show_cancelled_bookings_not_visible: Cancelled bookings are not visible
      presenter:
        created: created
        create: created
        updated: updated
        update: updated
        destroyed: deleted
        destroy: deleted
        completed: completed
        complete: completed
        cancelled: cancelled
        cancel: cancelled
        reopened: reopened
        reopen: reopened
        approved: approved
        autocompleted: Autocompleted by FlightLogger
        unknown_user: Unknown user
    user_programs:
      user_programs_index_container:
        header: Program changelogs
        records: "Total programs: %{program_count} (%{student_count} %{students_label})"
        active: Active
        standby: Standby
        completed: Completed
        discontinued: Discontinued
        deleted: Deleted
        students:
          one: "student"
          other: "students"
      table_structure:
        please_wait: Please wait. Programs are loading...
      tables:
        table: &general-table
          latest_change: Latest change
          enrollment: Enrollment
          student: Student
          program_name: Program
          revision: Revision
          updated_by: Updated by
          records: "Total programs: %{program_count} (%{student_count} %{students_label})"
          students:
            one: "student"
            other: "students"
        deleted:
          <<: *general-table
        no_programs:
          no_programs_description: No programs in the selected period
      filter_report_section:
        find_report: Find report
      presenter:
        created: created
        create: created
        updated: updated
        update: updated
        activated: activated
        active: activated
        completed: completed
        standby: standby
        discontinued: discontinued
        discontinue: discontinued
        destroyed: deleted
        destroy: deleted
        unknown_user: Unknown user
      show:
        user_program_title: "%{program_name} %{program_revision} changelog"
      user_program_details:
        cannot_show_changes: User program information missing
      user_program_changelog_card:
        complete: completed
        program_status: Program status
        assignment_date: "Enrolled: %{program_date}"
        attendance: attendance
        attendance_of_total: "(%{attended} of %{total})"
        completed_total: "(%{completed} of %{total})"
    changelog_container:
      header: Changelogs
      booking_title: "Booking changelog"
      user_program_title: "%{program_name} %{program_revision} changelog"
      return: Return to bookings
  maintenance_requirements:
    date_posttext: days
    time_posttext: hours
    cycle_posttext: cycles
    title: Maintenance requirements
    missing: Missing
    expired: Expired
    to_approval: Requiring approval
    defaults_description: 'A colored warning will appear when a requirement is:'
    entry_destription: 'A colored warning will appear after:'
    template_defaults:
      missing_template: 'Missing'
      to_approval_template: '{% if expiration_date == blank %}Updated{% else %}Updated with expiry date: {{expiration_date}} {% endif %}'
      expired_template: 'Expired: {{expiration_date}}'
  maintenance_warnings:
    title: "Maintenance warnings"
    requirers:
      plane: Aircraft requirement
      single_engine: Single engine requirement
      multi_engine: Multi engine requirement
      sim: Simulator requirement
    awaiting_approval: "Requiring approval"
    reply_title: "Regarding: %{warning_status} %{type} on %{call_sign}"
    in_maintenance_until: "Currently in maintenance (%{starts_at} - %{ends_at})"
    booked_for_maintenance: "Scheduled for maintenance (%{starts_at} - %{ends_at})"
    template_defaults:
      unknown: N/A
      missing: Missing
      about_to_run_out: |
        About to run out
        {% unless expiry_date == blank %} on {{expiry_date}} {% endunless %}
        {% unless time_left == blank %} of time in {{time_left}} hours {% endunless %}
        {% unless cycles_left == blank %} only {{cycles_left}} cycles left {% endunless %}
      to_approval: |
        Updated with
        {% unless serial_number == blank %} serial number: {{serial_number}} {% endunless %}
        {% unless expiry_date == blank %} date: {{expiry_date}} {% endunless %}
        {% unless expiry_airborne_time == blank %} airborne time: {{expiry_airborne_time}} {% endunless %}
        {% unless expiry_timer_time == blank %} timer time: {{expiry_timer_time}} {% endunless %}
        {% unless expiry_cycles == blank %} cycles: {{expiry_cycles}} {% endunless %}
      expired: |
        Expired
        {% unless expiry_date == blank %} on {{expiry_date}} {% endunless %}
        {% unless time_left == blank %} of time {{time_left}} hours ago {% endunless %}
        {% unless cycles_left == blank %} of {{cycles_left}} cycles overdue {% endunless %}
    status:
      missing: Missing
      expired: Expired
      to_approval: Requiring approval
      approved: Approved
      unapproved: Unapproved
    expired_template_date:
      one: "Overdue by %{days} day"
      other: "Overdue by %{days} days"
    expired_template_cycles:
      one: "Overdue by %{cycles} cycle"
      other: "Overdue by %{cycles} cycles"
    expired_template_tacho: "Overdue by %{time}"
    to_approval_template_serial: "%{serialNumber}"
    to_approval_template_date: "Expiry date: %{date}"
    to_approval_template_tacho: "Expiry time: %{time}"
    to_approval_template_cycles: "Expiry cycles: %{cycles}"
    will_expire_template_date:
      one: "Expires in %{days} day"
      other: "Expires in %{days} days"
    will_expire_template_tacho: "Expires in %{time}"
    will_expire_template_cycles:
      one: "Expires in %{cycles} cycle"
      other: "Expires in %{cycles} cycles"
    buttons:
      add_btn: "Add"
      approve_btn: Approve
      reject_btn: Reject
      renew_btn: Renew
    tooltip:
      generic_tooltip_expires_on_date: "Expires on date"
      generic_tooltip_tach: "Tach"
      generic_tooltip_tacho: "Time"
      generic_tooltip_expires_on_cycles: "Expires on cycles"
      generic_tooltip_serial: "Serial number"
  squawk_warnings:
    index:
      title: Discrepancy warnings
      sub_title: 'Warnings for selected filter: %{warnings_count}'
      filter: Filter
      call_sign: Call sign
      model: Model
      title_name: Title
      issue_description: Issue description
      status_name: Status
      created_by: Created by
      approved_by: Approved by
      severity: Severity
      actions: Actions
    title: Discrepancy warnings
    status:
      expired: Expired
      to_approval: Requiring approval
      approved: Approved
      unapproved: Unapproved
    buttons:
      approve_btn: Approve
      reject_btn: Reject
      resolve_btn: Resolve
    confirm_message:
      approve: Are you sure want to approve this discrepancy?
      reject: Are you sure want to reject this discrepancy?
      resolve: Are you sure want to resolve this discrepancy?
  ground_school_requirements:
    form:
      attempt_title: Attempt warning
      attempt_description: "A colored warning will appear after:"
      attempt_warning_type: attempts
      expiry_title: Expiry warning
      expiry_description: "A colored warning will appear after:"
      expiry_warning_type: months
      sitting_title: Sitting warning
      sitting_description: "A colored warning will appear after:"
      sitting_warning_type: sittings
  ground_school_warning_filters:
    sitting: Sitting
    expiry: Expiry
    attempt: Attempt
    all: All

  flight_time_warning_filters:
    all: All
    current: Current
    expiry_warning: Expiry warning
    expired: Expired
  theory_release:
    user_header_row:
      date: Date
      grade: Grade
      instructor: Instructor
      subject: Note
      student_comment: Student comment
      category: Subject
      attendance: Attendance / Duration
    user_row:
      confirm: Are you sure you want to delete this theory release?
      update: "Edit"
  theory_exam:
    user_row:
      confirm: Are you sure you want to delete this exam?
      update: Edit
    user_header:
      confirm: Are you sure you want to delete this exam?
    team_table_header:
      student: Student
      grade: Grade
      date: Date
      examiner: Examiner
      ref_num: Ref. number
      sitting: Sitting
    user_table_header:
      date: Date
      grade: Grade
      examiner: Examiner
      ref_num: Ref. No.
      course: Program
      category: Subject
      attempts: Attempts
      attendance: Attendance / Duration
  progress_test:
    team_header_row:
      student: Student
    user_header_row:
      create: Add Progress test
      date: Date
      grade: Grade
      instructor: Instructor
      subject: Note
      student_comment: Student comment
      student: "Student"
      attendance: Attendance / Duration
    user_row:
      update: Edit
      delete: Delete
      confirm: Are you sure you want to delete this progress test?
  type_questionnaire:
    team_header_row:
      student: Student
    user_header_row:
      create: Add Type Questionnaire
      date: Date
      grade: Grade
      instructor: Instructor
      subject: Note
      student_comment: Student comment
      student: "Student"
      attendance: Attendance / Duration
    user_row:
      update: Edit
      delete: Delete
      confirm: Are you sure you want to delete this type questionnaire?

  report_configuration:
    report_names:
      activities_report: Activities report
      aircraft_report: Aircraft report
      airports_report: Airports report
      production_report: Production report
      program_changelog: Program changelog
      invoice_report: Invoice report
      user_balance_report: Student & renter account overview
      accounting_transactions_report: Accounting transactions
      account_theory_report: Raw data - theory report
      activity_list_report: Operations report
      booking_report: Booking statistics
      cancelled_booking_report: Cancellation report
      flight_report: Raw data - flight report
      fuel_report: Fuel report
      logbook_report: Logbook
      theory_lectures_list_report: Theory report
      flight_duty_time_warning_report: Flight duty time warnings report
      departure_arrival_history_report: Departure arrival history report
      program_revision_time_report: Program revision time report
      duty_report: Raw data - duty report
  roles:
    cloudsync_admin: Data backup administrator
    maintenance_admin: Maintenance administrator
    instructor: Instructor
    ground_instructor: Ground instructor
    flight_instructor: Flight instructor
    administrator: Administrator
    guest: Guest
    signoff_admin: Need sign off administrator
    repetition_admin: Repetition administrator
    crew: Crew
    staff: Staff
    student: Student
    renter: Renter
    pic: PIC
    participant: Participants
    student_1: Student 1
    student_2: Student 2
    observer: Observer
    certificate_admin: Certificate administrator
    collaboration_admin: Collaboration administrator
    index:
      title: Roles
      subtitle: "%{role} permissions"
      active: Active
      deactivated: Deactivated
      name: Name
      permissions: Access
      members: Members
      missing: Missing
    role:
      user_count: '%{count} members'
    todo:
      memberships:
        active_all: All active users
        active_administrators: Active administrators
        deactivated_administrators: Deactivated administrators
        flight_instructors: Active flight instructors
        deactivated_flight_instructors: Deactivated flight instructors
        ground_instructors: Active ground instructors
        deactivated_ground_instructors: Deactivated ground instructors
        active_instructors: Active instructors
        deactivated_instructors: Deactivated instructors
        active_renters: Active renters
        deactivated_renters: Deactivated renters
        active_crew: Active crew
        deactivated_crew: Deactivated crew
        active_staff: Active staff
        deactivated_staff: Deactivated staff
        active_guest: Active guest
        deactivated_guest: Deactivated guest
        active_students: Active students
        completed_students: Completed students
        standby_students: Standby students
        __students__without_program: Students without program
        discontinued_students: Discontinued students
        invalid: "This scope #{name} is not valid"
      new:
        title: Group
      edit:
        title: Group
      show:
        title: Group
        name: Name
        members: Members
        group_type: System group
        no_members: There are no members in this group

      form:
        name: Name
        submit: Save
      new_group: Create group
      active_groups: Active groups
      deactivated_groups: Deactivated groups
      no_active_groups: There are no active groups to be shown at the moment.
      no_deactivated_groups: There are no deactivated groups to be shown at the moment.
      deactivate: "Are you sure, that you want to deactivate group: %{group_name}?"
      edit_btn: Edit
      groups: Groups
      flash_created: The group has been created
      flash_updated: The group has been updated
      flash_merge: The groups has been merged
      flash_merge_error: You have to choose a group to be merged.
      report:
        date: Date
        dual: Dual
        invoice: Invoice
        invoice_marked: Invoice
        invoice_number: Invoice number
        mark_all: Mark all
        sim: SIM
        solo: Solo

  titles:
    create: Create %{model}
    update: Update %{model}
    add: Add %{model}
    edit: Edit %{model}
  buttons:
    add: Add %{model}
    processing: Processing
    create: Create %{model}
    back_to: Back to %{model}
    update: Update %{model}
    save: Save %{model}
    destroy: Delete %{model}
    resolve: Resolve %{model}
    reject: Reject %{model}
  buttons_helper:
    activate: Activate
    clone: Clone
    deactivate: Deactivate
    delete: Delete
    edit: Edit
    add: Add
    share: Share
    copy_link: Copy link
    move: Move
    show: Show
    end: End
    calendar_sync: Calendar synchronization
    not_allowed: This function is disabled due to validation error
  background_renders:
    create:
      errors:
        invalid_date: A date entered is not valid!
        argument_error: A date entered is not valid!
  program_import:
    done_validating: 'Spreadsheet validated successfully! Now waiting for import to start ...'
    working: 'Validating spreadsheet ...'
    something_went_wrong: 'ERROR: An unknown error occurred. The spreadsheet was not loaded!'
    done_importing: The program was imported successfully
    syllabus_validation_error: 'The following lesson is invalid: %{name}.<br><ul><li>%{messages}.</li></ul>'
    errors:
      io_error: 'Error: The file was not found'
      program_name_missing: 'Error: The program name is not specified'
      revision_name_missing: 'Error: The Revision name is not specified'
      wrong_headers: 'ERROR: The spreadsheet does not have the expected headers in row 1!'
      empty_exercises: 'ERROR: The spreadsheet is not allowed to have empty exercises!'
      absense_of_stop: 'ERROR: The last line should include STOP!'
      no_load_sheet: 'ERROR: The spreadsheet does not have a sheet named Program!'
      file_error: 'ERROR: The uploaded file seems to be in a wrong format!'
      lesson_missing: 'ERROR: Every new phase requires a new lesson to be specified'
      category_missing: 'ERROR: Every new lesson requires a new category to be specified'
  user_importer:
    done_validating: 'Spreadsheet validated successfully! Now waiting for import to start ...'
    working: 'Validating spreadsheet ...'
    something_went_wrong: 'ERROR: An unknown error occurred. The spreadsheet was not loaded!'
    done_importing: The users were imported successfully
    errors:
      wrong_headers: 'ERROR: The spreadsheet does not have the expected headers in row 1!'
      absense_of_stop: 'ERROR: The last line should include STOP!'
      no_load_sheet: 'ERROR: The spreadsheet does not have a sheet named Users!'
      file_error: 'ERROR: The uploaded file seems to be in a wrong format!'
      wrong_user_type: 'ERROR: Only S,I,FI,GI,A,R,C - or / separated combination - allowed in first column (student, instructor, administrator, renter or crew)!'
      wrong_emails: 'ERROR: Column 6 should consist of valid emails!'
      wrong_organization: "ERROR: The spreadsheet does not have the expected organizational domain headers!"
      not_matching_organization: "ERROR: Organizational domain in file does not match chosen organization. The spreadsheet was not loaded!"
  errors:
    messages:
      invalid_format: invalid format
      too_high: too high
      above_range:
        one: of %{max} days surpassed with %{count} day
        other: of %{max} days surpassed with %{count} days
    attributes:
      program_phase:
        contains_normal_lectures?: The phase cannot be deleted because it contains lessons
        last_program_phase?: The revision must contain at least one phase
      account_setting:
        invoice_emails:
          empty: "Must include at least one email address"
          invalid_email: "Contains the invalid email address: '%{email}'"
  flash:
    activated: The %{model} has been activated
    not_activated: The %{model} could not be activated
    destroyed: The %{model} has been deleted
    not_destroyed: The %{model} could not be deleted
    deactivated: The %{model} has been deactivated
    not_deactivated: The %{model} could not be deactivated
    created: The %{model} has been created
    updated: The %{model} has been updated

  delete_confirm: Are you sure you want to delete the %{model}
  globals:
    open: Open
    tracker_provider:
      flight_radar: Flightradar24
      radar_box: AirNav RadarBox
      flight_aware: FlightAware
      spidertracks: Spidertracks
    all: All
    aircraft: Aircraft
    flight_logs:
      primary: Primary
      primary_with_type: Primary (%{type})
      secondary: Secondary
      secondary_with_type: Secondary (%{type})
      tertiary: Tertiary
      tertiary_with_type: Tertiary (%{type})
    aircraft_type:
      liters: L
      USG: USG
      qt: qt
      block: Block
      airborne: Airborne
      tacho: Timer
      timer: Timer
      airplane: Airplane
      helicopter: Helicopter
      simulator: Simulator
      airswitch: Airswitch
      hobbs: Hobbs
      heater_hobbs: Heater Hobbs
      tach: Tach
      VDO: VDO
      DATCON: DATCON
      EDU: EDU
      flight_time: Flight time
      short_decimal: Decimal (100.5)
      hours_minutes: Hours Minutes (100:30)
      timestamp: Timestamp (13:37)
      decimal: Decimal (100.50)
      airborne_timer: Airborne
      block_timer: Block
      tach_timer: Tach
    pilot_flying:
      pilot_flying: Pilot Flying
      pilot_monitoring: Pilot Monitoring
      pilot_not_specified: Not specified
      short:
        pilot_flying: PF
        pilot_monitoring: PM
        pilot_not_specified: Not specified
    aircraft_engine_type:
      single_engine: Single-engine
      multi_engine: Multi-engine
      simulator: Simulator
    completed: Completed
    cancelled: Cancelled
    classrooms: Classrooms
    instructors: Instructors
    activate: Activate
    deactivate: Deactivate
    instructors_and_crew: Instructors and crew
    save: Save
    update: Update
    delete: Delete
    edit: Edit
    add: Add
    reject: Reject
    approve: Approve
    renew: Renew
    show: Show
    students: Students
    staff: Staff
    crew: Crew
    renters: Renters
    administrators: Administrators
    guests: Guests
    call_sign: Call sign
    today: Today
    total: Total
    theory:
      empty_subject: "There is no subject"
      empty_comment: "There is no comment"
      empty_instructor: "There is no instructor"
      no_comment: "There are no comments yet"
    report:
      creation_completed: Report creation completed
      previous_page: Press OK to return to previous page
    ok: OK
    default_program_phase_name: "Phase 1"
    maintenance_warnings: Maintenance warnings
    certificate_warnings: Certificate warnings
    copy_to_clipboard: "Press to copy to clipboard"
    lesson: "Lesson"
    mfa:
      mfa_required: "Activate 2-Factor Authentication in order to access %{company}"
  global_reports:
    pdf: PDF
    csv: CSV (Excel)
    xml: XML
    export_as: Export as
    find_report: Find report
    loading_text: It can take a while to generate this report. Please have patience
  flightlogger:
    roles: User roles
    rental_roles: "Rental permissions"
    additional_roles: "Additional permissions"
    departure_arrival_roles: "Departure/Arrival permissions"
  activity_list_report:
    title: '%{activity}'
  theory_lectures_list_report:
    title: 'Theory lecture report on %{date}'
  customer_list_report:
    title: '%{state} customers list'
  user_balance_report:
    report:
      report_for: '%{user} - Student/renter account overview'
      admin: Admin
      empty_balance: There are no transactions in the selected time interval
      current_balance: 'Current balance:'
      balance: Balance
      comment: Comment
      date: Date of registration
      instructor: Instructor
      instructor_reference: Instructor reference
      price: Price
      student: Stu/Ren
      student_reference: Stu/Ren reference
      time: Time
      title: Accounting transactions
      student_title: Balance
      tx_id: Trans. number.
      tx_type: Trans. type
      registration: Registration
      total: Total
      activity_date: Date of activity
      flight_id: Flight id
  accounting_transactions_report:
    report:
      total: Total
      admin: Admin
      balance: Balance
      comment: Comment
      date: Date of registration
      instructor: Instructor
      instructor_reference: Instructor reference
      price: Price
      student: Stu/Ren
      student_reference: Stu/Ren reference
      time: Time
      title: Accounting transactions
      student_title: Student/renter account overview
      tx_id: Trans. number.
      tx_type: Trans. type
      registration: Registration
      empty_transactions: "There are no transactions yet"
      activity_date: Date of activity
      flight_id: Flight id
  accounting_sales_report:
    report:
      report_for: Accounting sales from %{from} - %{to}
      title: Accounting sales
      name: Name
      payment: Payment
      deduction: Deduction
      result: Result
      empty_sales_reports: "There are no payments/deductions yet"
  accounting_balances_report:
    report:
      report_for: Accounting balances on %{on}
      title: Accounting balances
      balance: Balance
      call_sign: Call sign
      name: Name
      date: 'Date:'
      empty_accounting_balances: "No balances differ from 0 on the given date"
  aircraft_report:
    report:
      report_for: Aircraft report from %{from} - %{to}
      airport: Airport
      approach: Approach
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      date: Date
      flights: Flights
      full_stop: Full stop
      full_stop_and_touch_and_go: FS + T&G
      go_arounds: Go-around
      landings: Landings
      pilot: Ins/Crew
      student_renter: Stu/Ren/Cus
      type: Type
      departure: Departure
      arrival: Arrival
      total: Total
      total_amount: Total
      touch_and_go: Touch and go
      empty_flight: There are no flights yet
      empty_landing: There are no landings yet
      flight_id: Flight ID
      fuel_coefficient: Fuel coefficient
      calculated_fuel: Calculated fuel
      measurement:
        liters: L
        USG: USG
        qt: qt
  airports_report:
    report:
      report_for: Airports report from %{from} - %{to}
      approach: APP
      date: Date
      full_stop: FS
      go_arounds: GA
      landings: Landings
      plane: Aircraft
      airport: Airport
      pilot: Ins/Crew
      total: Total
      touch_and_go: T & G
      empty_landing: There are no landings yet
      student_renter: Stu/Ren/Cus
      fuel_coefficient: Fuel coefficient
      calculated_fuel: Calculated fuel
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      flight_id: Flight ID
      type: Type
      flightable_types:
        user_lecture: School flight
        rental: Rental
        trip: Operation
      secondary_start: "Secondary start"
  production_report:
    report:
      activity: Movements
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      landings: FS + T&G
      rentals: Rentals
      total: Total
      total_primary: Total primary
      empty_flights: "There are no flights yet"
      plane: Aircraft
  invoice_report:
    income_row:
      total: Total primary
    expenses_row:
      total: Total primary
    report:
      report_for: Invoice report for %{from} - %{to}
      total: Total
      empty_income: There is no income yet
      empty_expenses: There are no expenses yet
      income: Income
      expenses: Expenses
      duty_time: Duty time
      name: Name
      training: Training
      operation: Operation
      rental: Rental
      landing: Landing
      class_theory: Class theory
      progress_test: Progress test
      theory_release: Theory release
      exam: Exam
      type_questionnaire: Type questionnaire
      extra_theory: Extra theory
      cancellation: Cancellation
  invoice:
    created: The invoice was created
    paid: Paid
    charge_failed: Charge failed
    not_charged: Not yet charged
    account_no_stripe_id: Account is not registered in Stripe.
    account_no_credit_card: Account has no active credit card
    invoice_cancelled: Invoice was cancelled by administrator
    not_enough_funds: Account credit card has insufficient funds. Contact %{emails}
    authentication_required: Payment requires authentication. Contact %{emails}
    credit_card_expired: Credit card expired. Contact %{emails}
    not_verified: "Not verified"
    stolen_or_lost: Account credit card is marked as lost or stolen. Contact %{emails}
    generic_decline: Charge on credit card declined. Contact %{emails}
    default_error: Charge error, unknown reason. Investigate reason, then contact %{emails}
    default_fail: Charge failed, unknown reason. Investigate reason, then contact %{emails}
    card_error_details:
      insufficient_funds: It was declined due to insufficient funds on credit card.
      stolen_or_lost: Credit card is marked as lost or stolen.
  email_template:
    checkin_mails: Departure/arrival emails
    checkin_eta_notification: Checkin has exceeded ETA
    checkin_fuel_notification: Checkin has exceeded fuel endurance
    user_mails: User emails
    booking_mails: Booking emails
    aircraft_mails: Aircraft Discrepancy emails
    checking_mails: Checkin/out emails
    lecture_mails: Lesson emails
    sms_mails: SMS module emails
    user_welcome_guest: Guest welcome
    user_welcome_renter: Renter welcome
    user_welcome_student: Student welcome
    user_welcome_instructor: Instructor welcome
    user_welcome_crew: Crew welcome
    user_welcome_staff: Staff welcome
    user_welcome_administrator: Administrator welcome
    user_reset_password: Instructions to new password
    booking_notify_booker: Rental request has been created
    booking_notify_about_approval: Rental request has been approved
    booking_notify_about_cancel: Booking has been cancelled
    booking_notify_about_delete: Booking has been deleted
    booking_notify_about_removal: User has been removed from booking
    booking_notify_renter_about_decline: Rental request has been declined
    booking_notify_renter_about_cancel: Rental request has been cancelled (Renter)
    booking_notify_users: Booking has been created
    booking_notify_users_edited_booking: Booking has been changed
    booking_rental_request_cancel_admin: Rental request has been cancelled (Admin)
    booking_notify_lecture_request_approved: Student booking request has been approved
    booking_notify_lecture_request_rejected: Student booking request has been declined
    booking_notify_lecture_request_new: New Student booking request
    lecture_repetition_needed: Repetition lesson has been created
    lecture_new_lecture: Lesson is awaiting approval
    sms_new_safety_hazard_report: SMS report has been created
    messagecenter_mails: Message center emails
    messagecenter_normal: Normal
    messagecenter_reply: Reply
    messagecenter_signoff: Need sign off
    messagecenter_mm: Maintenance
    messagecenter_cmm: Feedback
    certificate_mails: Certificate
    certificate_will_expire: Certificate will expire
    certificate_has_expired: Certificate has expired
    certificate_missing: Certificate missing
    collaboration_request: Collaboration request
    collaboration_request_accepted: Collaboration request accepted
    collaboration_request_rejected: Collaboration request rejected
    collaboration_mails: Collaboration emails
    payment_mails: Payment emails
    payment_request_ubm: UBM payment request awaiting FL pay
    payment_request_qim: QIM payment request awaiting FL pay
    payment_processed_ubm: UBM payment confirmation
    payment_processed_qim: QIM payment confirmation
    payment_refunded_ubm: UBM refund
    payment_cancellation_ubm: UBM payment cancelled
    payment_refunded_qim: QIM refund
    payment_cancellation_qim: QIM payment cancelled
    payment_reminder: Payment reminder
    payment_declined_qim: QIM payment declined
    payment_declined_ubm: UBM payment declined
    aircraft_squawk_warning_email: Discrepancy warning
    aircraft_squawk_subject_warning: Discrepancy created
    aircraft_squawk_name: discrepancy
  attachment:
    attachments: Attachments
    document:
      no_file: No file chosen
      choose_file: Choose document
      description: PDF Attachment
      submit: Upload
      confirm: Confirm
    image:
      description: Image Attachment
      delete_confirm: Are you sure you want to delete this image
  accounting_sales_reports:
    index:
      title: Accounting sales
      name: Student
      payment: Payment
      deduction: Deduction
      result: Result
      report_for: Accounting sales from %{from} - %{to}
      find_report: Find report
      filename_label: Accounting sales from %{from} - %{to}.csv
  account_balances:
    index:
      report_for: Accounting balances on %{on}
      date: 'Date:'
      filename_label: All balances at
      find_report: Find report
      title: Accounting balances
      update: Update
      delete: Delete
  conversations_mailer:
    subject:
      reply_anonymous: A user
    normal:
      button_text: See message
    cmm:
      button_text: See message
    mm:
      button_text: See message
    reply:
      button_text: See comment
    signoff:
      button_text: See message and sign off
      require_signoff: '%{creator} require you to sign off that you have read and understood this message!'
  invoices_mailer:
    unmanaged_accounting_error:
      subject: 'Accounting error'
    pre_charge_mail:
      subject: 'FlightLogger Invoice for %{month}'
    cancel_mail:
      subject: 'FlightLogger Invoice for %{month} is cancelled'
    warn_missing_funds:
      subject: "FlightLogger needs your attention: Credit card is lacking funding!"
    authentication_required:
      subject: "FlightLogger needs your attention: Payment requires authentication!"
    warn_missing_credit_card:
      subject: "FlightLogger needs your attention: Credit card is missing!"
    warn_credit_card_expired:
      subject: "FlightLogger needs your attention: Credit card has expired!"
    warn_credit_card_lost:
      subject: "FlightLogger needs your attention: Credit card marked as lost!"
    warn_generic_decline:
      subject: "FlightLogger needs your attention: Charge on credit card declined!"
    card_expired_warning:
      subject: "FlightLogger needs your attention: Credit card expired"
    card_close_to_expiration_warning:
      subject: "FlightLogger needs your attention: Credit card is near expiry"
    card_about_to_expire_warning:
      subject: "FlightLogger needs your attention: Credit card is near expiry"
  flight_time_limitation_types:
    new:
      create_flight_time_type: Create flight time requirement
    edit:
      update_flight_time_type: Update flight time requirement
    flight_time_limitation_type:
      deactivate: "Are you sure, that you want to disable the limitation: %{flight_time_limitation_type_name}?"
    index:
      create: Create requirement
      delete: Delete
      delete_confirm: Are you sure, that you want to delete the requirement '%{name}'
      type: Name
      title: Flight time requirements
      limitation: Limitation
      update: Update
      active: Active
      used_by: Used by
      deactivated: Deactivated
      no_active_flight_time_limitation_types: There are no active flight time requirements to be shown at the moment
      no_deactivated_flight_time_limitation_types: There are no deactivated flight time requirements to be shown at the moment
    form:
      defaults_description: 'A flight time limitation warning will appear after:'
      hours: hours
      include: 'Include:'
      expired_description: Expired
      warnings_description: Warnings
      within: within
      than: than
      limitation: 'Limitation: '
      flight_time_limitation_requirements_title: Additional warnings
      flight_time_limitation_requirements_no_types: There are no flight time limitation requirements to choose from
      triggers: triggers
      expired: expired
      warning: warning
      color: color
      new: Create requirement
      edit: Update requirement
  certificate_types:
    new:
      create_certificate_type: Create certificate requirement
    edit:
      update_certificate_type: Update certificate requirement
    certificate_type:
      deactivate: "Are you sure, that you want to disable the certificate: %{certificate_type_name}?"
    destroy:
      delete_flash: "The certificate and all associated certificates was deleted"
    index:
      create: Create requirement
      delete: Delete
      delete_confirm: Are you sure, that you want to delete the certificate '%{name}'
      name: Name
      title: Certificate requirements
      used_by: Used by
      type: Requirement
      update: Update
      active: Active
      deactivated: Deactivated
      no_active_certificate_types: There are no active certificate requirements to be shown at the moment
      no_deactivated_certificate_types: There are no deactivated certificate requirements to be shown at the moment
    form:
      certificate_requirements_title: Default warnings
      notify_via_email: "Notify via email"
      info_general: "A 'Standard' certificate may or may not include expiry and/or require document upload, and should be used for all standard certificates (e.g. passports, FI, airport IDs, etc.)"
      info_medical: "A 'Medical' certificate may or may not include expiry and/or require document upload. It also requires specifying the class (i.e. 1, 2, 3, LAPL), which the user needs to fulfill when uploading. The class (1, 2, 3, LAPL) is specified when adding the medical as a requirement."
      info_language: "A 'Language proficiency' certificate may or may not require document upload. The user must select the level (1-6) of the certificate when uploading. If the user uploads a level 6 certificate the language proficiency will have no expiry."
      new: Create requirement
      edit: Update requirement
      expire_on_date: "Expires on date"
      require_upload_of_document: "Require upload of document"
  maintenance_types:
    new:
      create_maintenance_type: Create maintenance requirement
    edit:
      update_maintenance_type: Update maintenance requirement
    trigger_on_time:
      airborne: Airborne
      timer: Timer
    maintenance_type:
      deactivate: "Are you sure, that you want to disable the maintenance: %{maintenance_type_name}?"
    destroy:
      delete_flash: "The maintenance and all associated maintenances was deleted"
    index:
      create: Create requirement
      delete: Delete
      delete_confirm: Are you sure, that you want to delete the maintenance '%{name}'
      name: Name
      title: Maintenance requirements
      used_by: Used by
      type: Requirement
      update: Update
      active: Active
      deactivated: Deactivated
      no_active_maintenance_types: There are no active maintenance requirements to be shown at the moment
      no_deactivated_maintenance_types: There are no deactivated maintenance requirements to be shown at the moment
    form:
      maintenance_requirements_title: Default warnings
      submit: Create requirement
      trigger_on_date: Expires on date
      trigger_on_log: Expires on log
      trigger_on_cycles: Expires on cycles
      require_documentation: Require upload of document
      require_serial_number: Require serial number
      new: Create requirement
      edit: Update requirement
    warning_date_form:
      description: 'Date warnings'
      posttext: days
    warning_log_time_form:
      description: 'Log warnings'
      posttext: hours
    warning_cycles_form:
      description: 'Cycles warnings'
      posttext: cycles
    hovers:
      trigger_on_date: Expires on date
      trigger_on_airswitch_time: Expires on airswitch time
      trigger_on_hobbs_time: Expires on hobbs time
      trigger_on_heater_hobbs_time: Expires on heater hobbs time
      trigger_on_tach_time: Expires on tach time
      trigger_on_VDO_time: Expires on VDO time
      trigger_on_DATCON_time: Expires on DATCON time
      trigger_on_EDU_time: Expires on EDU time
      trigger_on_flight_time_time: Expires on flight time
      trigger_on_airborne_time: Expires on airborne time
      trigger_on_block_time: Expires on block time
      trigger_on_cycles: Expires on cycles
      require_documentation: Require upload of document
      require_serial_number: Require serial number
    time_options:
      airborne: airborne
      timer: timer
  account_order_items:
    index:
      admin: Admin
      comment: Comment
      date: Date
      filename_label: Accounting transactions from
      instructor: Instructor
      instructor_reference: Instructor reference
      price: Price
      search: Search
      student: Stu/Ren
      student_reference: Stu/Ren reference
      time: Time
      to: to
      tx_id: Trans. number.
      tx_type: Trans. type
      registration: Registration
      report_for: Accounting transactions from %{from} - %{to}
      find_report: Find report
  authlogic:
    error_messages:
      email_invalid: should look like an email address
  activemodel:
    attributes:
      report_filter:
        max_range: Max date range
      certificate:
        certificate_type: Certificate
      certificate_requirement: &certificate_requirement
        certificate_type: Certificate
      document:
        file_url: You need to choose a file to upload a document
  activerecord:
    attributes:
      access_permission:
        aircraft_permission: Aircraft access
        aircraft_status: Can see aircraft status
        aircraft_maintenance_permission: Maintenance access
        booking_view_conflict_center: Can view conflict center
        booking_details_permission: Can see details of bookings
        booking_administrator: Can create & edit bookings
        booking_view_availabilities: Can view availabilities
        booking_view_cancelled: Can view cancelled bookings
        certificate_permission: Can read certificates
        certificate_manage_permission: Can add/edit/renew certificates
        certificate_approve_permission: Can approve certificates
        departure_arrival_can_dispatch_checkins_permission: Can dispatch
        departure_arrival_can_view_flight_types: Can view flight types
        program_permission: Can see and edit programs
        program_master_list_permission: Can see and edit master exercise lists
        program_cbta_permission: Can see and edit CBTA programs
        operation_permission: Other users' registrations
        operation_own_permission: Own registrations
        student_permission: Able to see students
        student_classes_permission: View, create and change classes
        student_create_new: Can create new students
        student_manage_user_program: Can add and remove programs from users
        student_change_program_state: Can change program state
        student_flight_program_permission: Proceed and change flight registrations
        student_flight_program_change_approved_lesson: Can change lessons approved by students
        student_ground_program_permission: Proceed and change ground registrations
        student_ground_program_change_exams: Can edit/delete exams
        student_view_documents: Can view documents
        student_view_bookings: Can view bookings
        student_view_reports: Can view reports
        student_flight_program_add_extra_lessons: Can add extra lessons
        student_flight_program_amend_credited_hours: Can amend credited hours
        student_ground_program_amend_credited_hours: Can amend credited hours
        rental_own_reg_permission: Own registrations
        rental_other_reg_permission: Other users' registrations
        report_accounting_balances_permission: Accounting balances
        report_accounting_sales_permission: Accounting sales
        report_accounting_transactions_permission: Accounting transactions
        report_aircraft_permission: Aircraft reports
        report_airports_permission: Airports reports
        report_booking_permission: Booking statistics
        report_cancelled_booking_permission: Cancellation report
        report_analytic_dashboard_permission: FlightLogger analytics
        report_fuel_permission: Fuel report
        report_invoice_permission: Invoice report
        report_production_permission: Production report
        report_duty_permission: Raw data - duty report
        report_flight_permission: Raw data - flight report
        report_account_theory_permission: Raw data - theory report
        report_user_program_changelog_permission: Program changelogs
        ubm_see_other_permission: Other users' accounting information
        ubm_see_own_permission: See own accounting information
        ubm_manage_program_prices_permission: Can manage program prices
      aircraft_model:
        name: Model
        aircraft_type: Type
        aircraft_class: Class

      certificate_requirement:
        <<: *certificate_requirement
      guest_setting:
        show_user_normal_contact: Show basic info
      flight_time_limitation_type:
        warning_measurements:
          block_hours: hours
          flights: flights
          landings: landings
      account:
        company: Company
        exercises: Exercises
        lectures: Lessons
        planes: Aircraft
        programs: Programs
        subdomain: Subdomain
        students_can_book: Students can see booking pages
        students_can_see_booking_details: Students can see booking details
        renters_can_book: Renters can see booking details
        crew_can_book: Crew can see booking pages
        crew_can_see_booking_details: Crew can see booking details
        instructors_can_book: Instructors can see booking pages
        instructors_can_see_booking_details: Instructors can see booking details
        staff_can_book: Staff can see booking pages
        staff_can_see_booking_details: Staff can see booking details
        users: Users
        use_emergency_contacts: Allow for emergency contact information on users
        shows_last_timer: Default timer start from total timer
        default_departure_airport: Default departure from current airport
        uses_booking_documents: Allow document attachments on bookings
        use_if: Allow IF time in lessons
        use_at: Allow Asymmetric time in lessons
        booking_email_notification: Default check mark in "Notify via email"
        cmm_ids: "Feedback Receiver(s)"
        maintenance_receiver_ids: "Maintenance Receiver(s)"
      aircraft_remark:
        subject: Title
        body: Text
      dropbox_synchronization_setting:
        sync_current_certificates: Synchronize current user certificates
        sync_previous_certificates: Synchronize previous user certificates
        sync_user_documents: Synchronize user documents
      certificate_type:
        name: Name
        certificate_class: Type
      account_setting:
        show_comments: Show comments in Departure/Arrival - dispatcher can still see if unchecked
        comment_from_booking: Departure comments default to booking comment
        checkin_eta_receivers: Receiver(s)
        checkin_fuel_receivers: Receiver(s)
        departure_hours: Hours
        default_report_paper_size: Paper size
        enable_flags: Enable flag functionality on lessons

        top_header_color: Navigation top bar color
        default_departure_fuel_last_flight: Default departure fuel from total fuel
        top_navigation_color: Navigation menu bar color
        top_middle_color: Navigation separator bar color
        booking_hover_delay: milliseconds before hover effect activates
        enable_guests: Enable guest users
        user_reference_is_unique: Require uniqueness of user reference field
        user_call_sign_is_unique: Require uniqueness of user call sign field

        flight_registration_minute_interval: minute intervals are used during flight registrations
        allow_registrations_without_booking: Allow registrations without a booking
        booking_selector_popup_proceeding_without_booking: Show booking selector popup when proceeding without booking
        show_accounting_students: Students
        show_accounting_staffs: Staffs
        show_accounting_crews: Crews
        show_accounting_instructors: Instructors
        show_accounting_renters: Renters

        use_acknowledgement_and_authorization: Enable acknowledgement and authorization
        dep_arr_warning_popup: Enable warning popup when dispatching
        dep_arr_warning_block_ack: Block if acknowledgement is missing
        dep_arr_warning_block_auth: Block if authorization is missing
        dep_arr_warning_block_squawk: Block if discrepancy is not airworthy
        dep_arr_warning_block_maintenance: Block if maintenance is missing, expired, or requiring approval
        dep_arr_warning_block_cert: Block if certificates are missing, expired, or requiring approval
        dep_arr_warning_block_need_sign_off: Block if sign off is missing
        dep_arr_warning_block_ftlm: Block if flight & duty time are above limitation
        dep_arr_warning_block_ftlm_disabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)
        dep_arr_warning_block_ground_school: Block if ground training warnings exist
        dep_arr_warning_block_accounting: Block if account balance is below 0
        dep_arr_warning_block_accounting_disabled: Block if account balance is below 0 (activate UBM module to enable this function)
        dep_arr_warning_block_accounting_reset_button: Reset to default

        booking_visibility_block: Enable blocking of booking visibility
        booking_visibility_block_administrator: Block administrators visibility of future bookings for more than
        booking_visibility_block_instructor: Block instructors visibility of future bookings for more than
        booking_visibility_block_crew: Block crew visibility of future bookings for more than
        booking_visibility_block_staff: Block staff visibility of future bookings for more than
        booking_visibility_block_student: Block students visibility of future bookings for more than
        booking_visibility_block_renter: Block renters visibility of future bookings for more than
        booking_visibility_block_guest: Block guest visibility of future bookings for more than
        booking_visibility_block_kiosk: Block kiosk visibility of future bookings for more than

        booking_flight_block: Enable blocking of flight bookings
        booking_flight_block_maintenance: Block if maintenance is missing, expired, or requiring approval
        booking_flight_block_cert: Block if certificates are missing, expired, or requiring approval
        booking_flight_block_need_sign_off: Block if sign off is missing
        booking_flight_block_ftlm: Block if flight & duty time are above limitation
        booking_flight_block_ftlm_disabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)
        booking_flight_block_ground_school: Block if ground training warnings exist
        booking_flight_block_accounting: Block if account balance is below
        booking_flight_block_accounting_disabled: Block if account balance is below (activate UBM module to enable this function)
        booking_flight_block_squawk: Block if discrepancy is not airworthy

        booking_theory_block: Enable blocking of theory bookings
        booking_theory_block_cert: Block if certificates are missing, expired, or requiring approval
        booking_theory_block_need_sign_off: Block if sign off is missing
        booking_theory_block_ftlm: Block if flight & duty time are above limitation
        booking_theory_block_ftlm_disabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)
        booking_theory_block_ground_school: Block if ground training warnings exist
        booking_theory_block_accounting: Block if account balance is below
        booking_theory_block_accounting_disabled: Block if account balance is below (activate UBM module to enable this function)

        departure_arrival_acknowledge_student_text: Student text
        departure_arrival_acknowledge_instructor_text: Instructor text
        departure_arrival_acknowledge_pic_text: Pilot in control text
        departure_arrival_acknowledge_renter_text: Renter text
        departure_arrival_acknowledge_crew_text: Crew text
        control_departure_arrival_from_booking_page: Control Departure/Arrival's status from the booking page (Dispatchers only)

        dep_arr_warning_block_ftlm_enabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)

        registration_flight_block: Enable blocking of flight registrations
        registration_flight_block_ack: Block if acknowledgement is missing
        registration_flight_block_auth: Block if authorization is missing
        registration_flight_block_maintenance: Block if maintenance is missing, expired, or requiring approval
        registration_flight_block_cert: Block if certificates are missing, expired, or requiring approval
        registration_flight_block_need_sign_off: Block if sign off is missing
        registration_flight_block_ftlm: Block if flight & duty time are above limitation
        registration_flight_block_ground_school: Block if ground training warnings exist
        registration_flight_block_accounting: Block if account balance is below 0
        registration_flight_block_ftlm_disabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)
        registration_flight_block_accounting_disabled: Block if account balance is below 0 (activate UBM module to enable this function)
        registration_flight_block_squawk: Block if discrepancy is not airworthy
        registration_theory_block: Enable blocking of theory registrations
        registration_theory_block_cert: Block if certificates are missing, expired, or requiring approval
        registration_theory_block_need_sign_off: Block if sign off is missing
        registration_theory_block_ftlm: Block if flight & duty time are above limitation
        registration_theory_block_ground_school: Block if ground training warnings exist
        registration_theory_block_accounting: Block if account balance is below 0
        registration_theory_block_ftlm_disabled: Block if flight & duty time are above limitation (activate FTLM module to enable this function)
        registration_theory_block_accounting_disabled: Block if account balance is below 0 (activate UBM module to enable this function)

        duration_format_hours_minutes: 'Hours Minutes (100:30)'
        duration_format_decimal: 'Decimal (100.50)'
        duration_format_short_decimal: 'Decimal (100.5)'
        duration_format_according_to_flight_log: 'According to flight log'
      member_setting:
        duration_format_hours_minutes: 'Hours Minutes (100:30)'
        duration_format_decimal: 'Decimal (100.50)'
        duration_format_short_decimal: 'Decimal (100.5)'
      report_configuration:
        duration_format_hours_minutes: 'Hours Minutes (100:30)'
        duration_format_decimal: 'Decimal (100.50)'
        duration_format_short_decimal: 'Decimal (100.5)'
        duration_format_seconds: 'Seconds (5430)'
        duration_format_minutes: 'Minutes (90)'
        duration_format_hours_minutes_seconds: 'Hours Minutes Seconds (1:30:30)'
      activity:
        name: Name
        note: Note
      airport:
        name: Name
        price: Price
      block:
        hours: Hours
        pay_date: Pay date
      booking/customer_bookable_resource:
        custom_lesson: 'Custom lesson %{name}'
        extra_lesson: 'Extra lesson %{name}'
        resource: Customer
        booking: Booking
      user_resources:
        instructor: Instructor
        crew: Crew
        staff: Staff
        student: Student
        renter: Renter
        pic: PIC
        participant: Participants
        student_1: Student 1
        student_2: Student 2
        observer: Observer
      checkin_acknowledgement:
        STUDENT_DEFAULT_TEXT: 'STUDENT ACKNOWLEDGEMENT<ul><li>Flight will be conducted in accordance with the Flight Training Operations Manual.</li><li>Flight will be conducted in accordance with the Aviation Regulations.</li><li>All weather and NOTAMS have been thoroughly reviewed for safety.</li><li>Student has received a pre-flight briefing from a qualified flight instructor.</li><li>Student has received all necessary preparatory ground instruction from a qualified flight instructor.</li><li>All pilot documents have been verified and are valid.</li><li>All PIlots accept the TERMS OF USE policy.</li></ul>'
      booking:
        activity: Operation
        user_resources: ""
        booking_documents: Documents
        classroom: Classroom
        comment: Comment
        ends_at: Debriefing
        flight_ends_at: End
        flight_starts_at: Start
        instructor: Instructor
        plane: Aircraft
        starts_at: Briefing
        subject: Lesson
        subject_category: Subject
        team: Class
        user: Student
        users: Students
        email_notice_on_change: Notify via email
      canceled_lecture:
        cancelation_date: Cancelation date
        comment: Comment
        instructor: Intructor
        reason: Reason
      cancelled_booking:
        comment: Comment
        cancellation_reason: Reason
        email_notification: Notify via email
      cancellation_reason:
        theory_exam: Exam
      category:
        can_have_extra_exercises: Can have extra exercises
        exercises: Exercises
        name: Name
        user_exercises: Exercises
      certificate:
        expiration_date: Expiration date
        issue_date: Issue date
        number: Number
        certificate_type: Certificate
        upload_certificate: Upload certificate
      classroom:
        name: Name
      customer:
        address: Address
        city: City
        company: Company
        email: E-mail
        full_name: Full name
        phone: Phone number
        zip_code: Post code
        country: Country
        country_code: Country
        state_code: State
      document:
        document_folder: Folder
        file: File
        name: Name
        file_url: You need to choose a file to upload a document
        crew: Crew
        staff: Staff
        instructor: Instructors
        renter: Renters
        student: Students
      document_folder:
        name: Name
        instructor: Instructors
        renter: Renters
        student: Students
        self_in_ancestors: Folder found in own ancestor hierachy
      email_message:
        message: Message
        recipients: Recipients
        subject: Lesson
      exercise:
        account: Account
        category: Category
        lecture: Lesson
        name: Name
        program: Program
        user_exercises: User exercises
        auto_flagged: Use on repetition
      extra_exercise:
        name: Name
        ranking: Ranking
      flight_time_header:
        short:
          vfr_dual: Dual
          vfr_solo: Solo
          vfr_spic: SPIC
          vfr_sim: Sim
          ifr_dual: Dual
          ifr_spic: SPIC
          ifr_sim: Sim
          multi_engine: ME
          night: NT
          cross_country: XC
          pilot_flying: PF
          pilot_monitoring: PM
          asymmetric: AT
          instrument: IF
        full:
          vfr_dual: VFR Dual
          vfr_solo: VFR Solo
          vfr_spic: VFR Spic
          vfr_sim: VFR Sim
          ifr_dual: IFR Dual
          ifr_spic: IFR SPIC
          ifr_sim: IFR Sim
          multi_engine: Multi Engine (ME)
          night: Night Time (NT)
          cross_country: Cross Country (XC)
          pilot_flying: Pilot Flying (PF)
          pilot_monitoring: Pilot Monitoring (PM)
          asymmetric: Asymmetric Training (AT)
          instrument: Instrument Flight (IF)
      flight:
        asymmetric_time: Asymmetric
        if_time: IF
        activity_type: Operation type
        approved_by_student: Approved by student
        arrival_airport: Arrival airport
        daytime: Daytime
        departure_airport: Departure airport
        flight_rules: Flight rules
        flight_type: Flight type
        instructor: Instructor
        landing: Landing
        landings: Landings
        off_block: Off block
        on_block: On block
        pilot_flying: Pilot role
        plane: Aircraft
        service_date: Service date
        service_timer: Next service at
        airborne_service_time: Next service at
        service_type: Service type
        simulator_class: Simulator type
        special_lecture: Special lesson
        timer_finish: Timer finish
        timer_start: Timer start
        user: User
        user_lecture: User lesson
        vdo: VDO
        airswitch: AIRSWITCH
        hobbs: HOBBS
        heater_hobbs: HEATER HOBBS
        tach: TACH
        VDO: VDO
        DATCON: DATCON
        EDU: EDU
        flight_time: FLIGHT TIME
        block: BLOCK
        airborne: AIRBORNE

      flight_hour:
        daytime: Daytime
        hours: Hours
        minutes: Minutes
        cross_country: Cross Country
        pilot_flying: Pilot role
        asymmetric_time_hour: Asymmetric
        if_time_hour: IF
      landing:
        airport: Airport
        flight: Flight
        number: Number
      lecture:
        description: Briefing
        dual_time: Dual time
        exercises: Exercises
        name: Name
        solo: Solo time
        vfr_dual: VFR Dual
        vfr_solo: VFR Solo
        vfr_spic: VFR SPIC
        vfr_sim: VFR Sim
        ifr_dual: IFR Dual
        ifr_spic: IFR SPIC
        ifr_sim: IFR Sim
        night: Night time
        multi_engine: Multi Engine (ME)
        cross_country: Cross Country (XC)
        instrument: Instrument Flight (IF)
        pilot_flying: Pilot Flying (PF)
        pilot_monitoring: Pilot Monitoring (PM)
        asymmetric: Asym. Training (AT)
        cant_be_greater_than_syllabus: must not exceed the syllabus time
      user_custom_lecture:
        solo_time: Solo time
        vfr_dual_time: VFR Dual
        vfr_solo_time: VFR Solo
        vfr_spic_time: VFR SPIC
        vfr_sim_time: VFR Sim
        ifr_dual_time: IFR Dual
        ifr_spic_time: IFR SPIC
        ifr_sim_time: IFR Sim
        me_time: Multi Engine (ME)
        cross_country: Cross Country (XC)
        if_time: Instrument Flight (IF)
        pilot_flying_time: Pilot Flying (PF)
        pilot_monitoring_time: Pilot Monitoring (PM)
        asymmetric_time: Asym. Training (AT)
      maintenance_part:
        maintenance_type: Maintenance
        expiration_date: Date
        expiration_airborne_time: Airborne time
        expiration_timer_time: Timer time
        expiration_cycles: Cycles
        serial_number: Serial number
        upload_certificate: Upload certificate
      old_message:
        message: Text
        title: Title
      plane:
        account: Account
        aircraft_type: Type
        airborne_time: Total airborne
        airborne_service_time: Next service at
        callsign: Call sign
        default_pmf: Default PM/PF value
        default_engine_type: Default SE/ME value
        enable_taxi_time: Enable taxi time
        enabled_pmf: Enable PM/PF
        fill_out_timer: Enable timer
        enable_timer_warning: Enable warning
        fill_out_airborne: Airborne time required
        flights: Flights
        landings_total: Total landings
        display_landings: 'Show in A/C status'
        total_fuel: Total fuel
        plane_class: Class
        plane_type: Model
        url: Flight tracker
        service_date: Service date
        slot_booking_enabled: Enable rental slots
        tach: Total tach
        VDO: Total VDO
        hobbs: Total hobbs
        heater_hobbs: Total Heater Hobbs
        airswitch: total airswitch
        DATCON: Total DATCON
        EDU: Total EDU
        airborne: Total Airborne
        block: Total Block
        flight_time: Total flight time
        service_timer: Next service at
        timer_warning_percent: Warning timer in %
        taxi_in_time: Default taxi in
        taxi_out_time: Default taxi out
        flight_tracker_registration: Registration
      program:
        account: Account
        exercises: Exercises
        lectures: Lessons
        name: Name
        user_programs: User programs
        users: Users
      program_revision:
        comment: Comment
        name: Name
        revision_template: Revision Template
      progress_test:
        grade: Grade
        instructor: Instructor
        subject: Lesson
        subject_category: Subject
        test_date: Date
        comment: Student comment
        user: Student
      rental:
        comment: Comment
        plane: Aircraft
      sitting:
        end_date: End date
        number: Number
        start_date: Start date
      sitting_group:
        theory_course: Theory course
        disable_other_groups: Deactivate previous sitting groups
        disabled: State
      special_lecture:
        approved_by_student: Approved by student
        description: Description
        flights: Flights
        instructor: Instructor
        title: Title
        user: User
      special_theory_lecture:
        description: Description
        instructor: Instructor
        user: User
      subject_category:
        name: Name
        total_seconds: Total time
      team:
        name: Name
      team_progress_test:
        comment: Comment
        instructor: Instructor
        subject: Note
        subject_category: Subject
        test_date: Date
      team_progress_test/progress_tests:
        user: Student
        grade: Grade
        instructor: Instructor
        subject_category: Subject
      team_theory_exam:
        exam_date: Date
        examiner: Examiner
        subject_category: Subject
      team_theory_exam/theory_exams:
        examiner: Examiner
        sitting: Sitting
        user: Student
        grade: Grade
        instructor: Instructor
        subject_category: Subject
      team_theory_release:
        instructor: Instructor
        release_date: Date
        subject_category: Subject
        subject: Note
      team_theory_release/theory_releases:
        user: Student
        grade: Grade
        instructor: Instructor
        subject_category: Subject
      team_type_questionnaire:
        subject_category: Subject
        subject: Note
      team_type_questionnaire/type_questionnaires:
        user: Student
        subject_category: Subject
        instructor: Instructor
      test_info:
        first_solo_date: First solo
        morse_date: Morse test
        solo_instructor: Solo instructor
        theory_expires_date: Theory expires date
        trial: Theory Release
        trial_instructor: Theory Release instructor
      theory_exam:
        exam_date: Date
        examiner: Examiner
        grade: Grade
        sitting_from: Sitting from
        sitting_to: Sitting to
        subject_category: Subject
        user: Student
      theory_lecture:
        comment: Comment
        ends_at: Ends at
        instructor: Instructor
        starts_at: Starts at
        subject: Lesson
        subject_category: Subject
        theory_lecture_users: ""
      theory_lecture/theory_lectures_users:
        attendance_status: Attendance
      theory_lectures_user:
        ends_at: To
        starts_at: From
        user: Student
        comment: Student comment
        attendance_status: Attendance
      theory_release:
        instructor: Instructor
        release_date: Date
        subject_category: Subject
        comment: Student comment
        user: Student
      todo_item:
        completed: Completed
        description: Description
        instructor: Instructor
        user: User
      trip/flights:
        user_resources: ""
      trip:
        address: Adress
        arrival_airport: Arrival airport
        city: City
        comment: Comment
        customer: Customer
        departure_airport: Departure airport
        full_name: Full name
        pic: PIC
        crew: Crew
        off_block: Off block
        on_block: On block
        phone: Phone
        plane: Aircraft
        timer_finish: Timer end
        timer_start: Timer start
        zip_code: Post code
        country: Country
      type_questionnaire:
        instructor: Instructor
        plane_type: Aircraft model
        review_date: Date
        comment: Student comment
        user: Student
        subject_category: Subject
      user:
        account: Account
        address: Address
        administrator: Administrator
        caa_ref_num: CAA Ref. number
        call_sign: Call sign
        city: City
        country: Country
        country_code: Country
        state_code: State
        crypted_password: Crypted password
        date_of_birth: Date of birth
        email: Email
        email_confirmation: Email confirmation
        unconfirmed_email: Email
        reference: User reference
        emergency_address: Address
        emergency_city: City
        emergency_email: E-mail
        emergency_first_name: First name
        emergency_last_name: Last name
        emergency_phone: Phone
        emergency_relation: Relationship
        emergency_zipcode: Post code
        emergency_country_code: Country
        emergency_state_code: State
        first_name: First name
        flights: Flights
        public_note: Public note
        admin_note: Admin note
        instructor_note: Instructor note
        gender: Gender
        instructor: Instructor
        instructor_flights: Instructor flights
        instructor_lectures: Instructor lectures
        instructor_todo_items: Instructor todo items
        last_name: Last name
        owner: Owner
        current_password: Current
        password: New
        password_confirmation: Re-type new
        password_salt: Password salt
        current_password_no_match: does not match your current password
        perishable_token: Perishable token
        persistence_token: Persistence token
        mobile_phone: Mobile phone
        nationality: Nationality
        phone: Phone
        program: Program
        renter: Renter
        special_lectures: Special lectures
        student: Student
        time_zone: Account & booking
        flight_time_zone: Flight registration
        theory_time_zone: Theory registration
        todo_items: Todo items
        user_exercises: User exercises
        user_lectures: User lectures
        user_program: User program
        zipcode: Post code
      user_availability:
        equal_times: "^Start- and end times can't be equal"
        end_before_start: "^Start time can't be later than end time"
        no_overlap: "Duty times cannot overlap"
      user_document:
        file: Document
        name: Name
        visible_to_user: Visible to the user
        visible_to_other: Visible to other
      user_exercise:
        comment: Comment
        exercise: Exercise
        ranking: Ranking
        user: User
        user_lecture: User lecture
      user_lecture:
        done: Done
        extra_exercises: Extra Exercises
        instructor: Instructor
        lecture: Lecture
        briefing: Briefing comments
        remarks: Debriefing comments
        user_exercises: User exercises
        no_flags: You need to flag at least one exercise to be able to create a repetition lesson
        submitted_by_instructor_at: Instructor registration
        approved_by_student_at: Student approval
      user_program:
        program: Program
        status: Status
        todo_items: Hold items
        user: User
      user_setting:
        calendar_certificate_feed: Include certificate expiry
        error_message_filter_selection: Could not update booking filter selection.
      users_team:
        team: Class
        user: Student
        user_id: Student
      plane_availability:
        equal_times: "^Start- and end times can't be equal"
        end_before_start: "^Start time can't be later than end time"
        in_past: "^Not allowed to update events in the past"
        delete_in_past: "^Not allowed to delete events in the past"
      squawk_description:
        severities:
          airworthy: Airworthy
          grounded: Not airworthy
    errors:
      full_messages:
        format: "%{attribute}%{message}"
      messages:
        accepted: must be accepted
        blank: must be specified
        confirmation: doesn't match confirmation
        empty: can't be empty
        equal_to: must be equal to %{count}
        even: must be even
        exclusion: is reserved
        greater_than: must be greater than %{count}
        greater_than_or_equal_to: must be greater than or equal to %{count}
        inclusion: must be specified
        invalid: is invalid
        less_than: must be less than %{count}
        less_than_or_equal_to: must be less than or equal to %{count}
        not_a_number: is not a number
        not_an_integer: must be an integer
        odd: must be odd
        record_invalid: 'Validation failed: %{errors}'
        taken: has already been taken
        too_long: is too long (maximum is %{count} characters)
        too_short: is too short (minimum is %{count} characters)
        wrong_length: is the wrong length (should be %{count} characters)
        required: must be specified
        no_active_recipients: The selected recipients does not include any active users
        message_not_found: Message not found. Either the message does not exist or you do not have access to it.
        external_reference_unique: has to be unique
      models:
        booking:
          attributes:
            starts_at:
              not_a_date: is not a date, please choose a valid date and try again
            ends_at:
              not_a_date: is not a date, please choose a valid date and try again
        user_lecture:
          attributes:
            status:
              inclusion: must be specified
        certificate:
          attributes:
            level:
              inclusion: must be specified
        theory_lectures_user:
          attributes:
            attendance_status:
              inclusion: must be specified
        theory_lecture:
          attributes:
            theory_lectures_users:
              too_short: '^Please select at least one student'
        team_progress_test:
          attributes:
            progress_tests:
              too_short: '^Please select at least one student'
        team_theory_release:
          attributes:
            theory_releases:
              too_short: '^Please select at least one student'
        team_theory_exam:
          attributes:
            theory_exams:
              too_short: '^Please select at least one student'
        team_type_questionnaire:
          attributes:
            type_questionnaires:
              too_short: '^Please select at least one student'
        order_items:
          attributes:
            balance:
              greater_than: must be no less than %{count}
              less_than: must be no greater than %{count}
      template:
        body: 'There were problems with the following fields:'
        header:
          one: 1 error prohibited this %{model} from being saved
          other: "%{count} errors prohibited this %{model} from being saved"
    models:
      account: Account
      account_certificate_type: Certificate type
      activity: Operation
      airport: Airport
      sitting_requirement: Sitting warning
      sitting_warning: Sitting warning
      attempt_requirement: Attempt warning
      attempt_warning: Attempt warning
      expiry_requirement: Expiry warning
      expiry_warning: Expiry warning
      booking: Booking
      canceled_lecture: Cancelled lecture
      cancellation_reason: Cancellation reason
      cancelled_booking: Cancelled booking
      category: Category
      certificate: Certificate
      certificate_type: Certificate
      certificate_requirement: Certificate requirement
      certificate_requirement_warning: Warning
      flight_time_limitation_requirement: Flight time limitation requirement
      flight_time_limitation_requirement_warning: Warning
      duty_time_limitation_requirement: Duty time limitation requirement
      duty_time_limitation_requirement_warning: Warning
      classromm: Classroom
      customer: Customer
      document: Document
      document_folder: Folder
      email_message: Email message
      event_entry: Warning
      exercise: Exercise
      extra_exercise: Extra exercise
      flight: Flight
      flight_hour: Flight Hour
      flight_time_limitation: Block Time Warning
      landing: Landing
      lecture: Lecture
      maintenance_type: Maintenance
      maintenance_part: Maintenance part
      maintenance_requirement: Maintenance requirement
      maintenance_requirement_warning: Warning
      maintenance_requirement_date_warning: Warning
      maintenance_requirement_log_time_warning: Warning
      maintenance_requirement_cycle_warning: Warning
      old_message: Wall post
      order_type: Transaction type
      plane: Aircraft
      plane_class: Aircraft class
      program: Program
      program_revision: Revision
      progress_test: Progress test
      price_list_items: Aircraft price
      rental: Rental
      sitting: Sitting
      special_lecture: Special lecture
      special_theory_lecture: Extra theory
      subgrade: Subgrade
      subject_category: Subject
      shift: Weekly schedule
      shift_exception: Exception
      team:
        one: Class
        other: Classes
      team_progress_test:
        one: Progress test
        other: Progress tests
      team_theory_exam:
        one: Exam
        other: Exams
      team_theory_release:
        one: Theory release
        other: Theory releases
      theory_exam:
        one: Exam
        other: Exams
      theory_lecture:
        one: Class theory
        other: Class theories
      theory_lectures_user:
        one: Class theory
        other: Class theories
      theory_release:
        one: Theory release
        other: Theory releases
      todo_item: Todo item
      trip: Trip
      team_type_questionnaire: Type questionnaire
      type_questionnaire: Type questionnaire
      user: User
      user_exercise: User exercise
      user_lecture: User lecture
      user_program: User program
      users_team: Class
      customer_document: Document
      customer_document_folder: Folder
      user_document: Document
      user_document_folder: Folder
      plane_document: Document
      plane_document_folder: Folder
    disabled_display_name: "%{name} (Deactivated)"
  activities:
    activity:
      activity_empty: There are no operations yet
    activity_item:
      deactivate: "Are you sure, that you want to deactivate operation: %{operation_name}?"
    activity_table:
      activate: Activate
      update: Edit
    administrator_index:
      active: Active
      deactivated: Deactivated
      activities: Operations
      create_new_activity: Create Operation
      name: Name
      old_activities: Deactivated operations
    flash_activated: The operation has been activated
    flash_not_activated: The operation could not be activated
    flash_deactivated: The operation has been deactivated
    flash_not_deactivated: The operation could not be deactivated
    index:
      fly_an_activity: Fly a %{name}
    old:
      active_activities: Active operations
      old_activities: Deactivated operations
    show:
      instructor: Instructor
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      confirm_activity_destroy: Are you sure you want to delete the operation?
      date: Date
      delete: Delete
      fly_an_activity: Fly a %{name}
      pic: PIC
      name: Name
      phone: Phone
  admin:
    accounts:
      show:
        table_header: Grades
        new: Create
        name: Name
        abbreviation: Abbreviation
        value: Value
        grade: Grade
        default: Default
        carry_forward: Carry Forward
        subgrade: Subgrade
        autoflag: Autoflaggit
        cf_headline: Carry Forward Headline
        save: Save headline
        updating: Updating Account
        statistics: Statistics
        s3_title: "s3 data"
        s3_get_data: "Refetch data"
        s3_data_size: "Account S3 usage %{size}"
        s3_data_amount: "Account data entries %{amount}"
        s3_bucket: "Account bucket %{bucket}"
        s3_last_call_request_cost: "Price of hitting refetch data was $%{price}"
        s3_last_run_at: "Last fetched at %{date}"
        s3_data_not_found: "No data found"
      create:
        success: The account has been created
      update:
        success: The account has been updated
      edit:
        enable_instructor_briefing_rate: "This checkbox is only enabled if 'Allow instructor briefing time in lesson' is checked at the top of this page."
        suspend_organization: "Suspend organization"
        booking_conflict: Booking Conflict
        booking_conflict_description: Enable Booking Conflict
        analytic_dashboard: FlightLogger Business Insights Module (BIM)
        analytic_dashboard_update_widget_payment_type: Update widgets payment types
        suspend_organization_due_to_lack_of_payment: "Suspend organization due to lack of payment"
        fl_pay_section:
          title: Flightlogger Pay
          api_username: API Username
          api_password: API Password
          security_header: Security Header
          additional_information_title: Additional Information
          legal_company_name: Legal Company Name
          address: Address
          city: City
          state: State (U.S.)
          zip_code: Zip Code
      new:
        account_info: Account info
        user_info: User info
        create_organization: "Create organization"
        owner: "Owner"
        create_new_organization: "Create new organization"
      index:
        organizations: "Organizations"
        create_organization: "Create organization"
        deactivate_organizations: "Deactivate organizations"
        confirm_message: Are you sure you want to disable this account?
      cloudsync_resync:
        success: "Re-sync job queued for providers: %{providers}"
      create_stripe_customer:
        success: "Stripe customer created for account %{subdomain}"
      old:
        organizations: "Organizations"
        active_organizations: "Active organizations"
    announcements:
      index:
        announcements: Announcements
        upcoming: Upcoming
        current: Current
        past: Past
        published_at: Published At
        publishing_at: Publishing At
        subject: Subject
        users_reached: Users Reached
      create:
        success: Announcement successfully created
      update:
        success: Announcement successfully updated
      destroy:
        success: Announcement successfully deleted
      end_now:
        success: Announcement successfully ended
      form:
        announcement_accounts: 'Select the accounts you want to reach:'
        user_groups: 'Select the user groups you want to reach:'
        announcement_contributor_accounts: 'Select the accounts that contributed:'
        subject: Subject
        time_zone: "Times shown in %{time_zone}."
        published_at: 'Announced at:'
        available_until: 'Available until (Keep it empty for indefinite ones)'
        footer: 'Text for the footer of the announcement:'
        save: Save
        updating: Updating
        show_announcement: Show this announcement
        back_to_announcements: Back to announcements
      show:
        announcement_details: Announcement Details
        announcement_accounts: 'Accounts you want to reach:'
        announcement_reached_percentage: " - %{users_reached} out of %{users_total} users has been reached"
        user_groups: 'User groups you want to reach:'
        announcement_contributor_accounts: 'Accounts that contributed:'
        subject: 'Subject:'
        published_at: 'Announced at:'
        available_until: 'Available until:'
        footer: 'Footer of the announcement:'
        edit_announcement: Edit this announcement
        destroy_announcement: Destroy this announcement
        back_to_announcements: Back to announcements
      announcement_accounts_selection:
        announcement_accounts: 'Select the accounts you want to reach:'
      quick_selection_buttons:
        all_active: Select all active
        all_deactivated: Select all deactivated
        all: Select all
        remove_selected: Remove all selections
      empty_announcements:
        empty_message: There are no announcements yet
      announcement:
        confirm_delete: Are you sure you want to delete the announcement?
        confirm_end: Are you sure you want to end the announcement early?
        end_announcement: End announcement
      preview_section:
        preview: PREVIEW
    grades:
      new:
        create: Create New Grade
      form:
        grade: Grade
        default: Default
        carry_forward: Carry Forward
      edit:
        update: Update Grade
    invoices:
      new:
        account: Account
      index:
        accounts_with_open_invoices: "Accounts with open invoices"
        active: "Active accounts"
        deactivated: "Disabled accounts"
  cloudsync_providers:
    onedrive: OneDrive
    dropbox: Dropbox
  flight_tracking_providers:
    radarbox: AirNav RadarBox
  cloudsync_provider_subtypes:
    onedrive:
      personal: OneDrive (Personal)
      business: OneDrive (Business)
  account_settings:
    save: Save
    reset: Reset to default
    save_success: Settings saved successfully.
    save_failure: An error occurred when saving!
    index:
      title: Account settings
      kiosk_user: Kiosk solution
      appearance_settings: Appearance
      booking_settings: Booking
      accounting_settings: Accounting (automatic payment)
      email_settings: Email templates
      report_settings: Report settings
      general_settings: General settings
      sam_settings: User Balance Module (UBM)
      sam_disabled: User Balance Module (UBM)
      ftlm_settings: Flight Time Limitation Module (FTLM)
      ftlm_disabled: Flight Time Limitation Module (FTLM)
      quickbooks_settings: QuickBooks Integration Module (QIM)
      fl_pay: FlightLogger Payments (PAY)
      flight_tracker_settings: Flight tracking
      theory_settings: Ground training
      data_backup_module: |
        Data Backup Module
        (DBM)
      data_backup_module_disabled: |
        Data Backup Module
        (DBM)
      departure_arrival_settings: Departure arrival
      user_settings: User settings
      user_roles_settings: Designated user roles
      cbta_settings: Competency-Based Training and Assessment (CBTA-Pro)
      cbta_disabled: Competency-Based Training and Assessment (CBTA-Pro)
      gdpr_settings: GDPR
      sms_settings: Safety Management System (SMS)
      analytics_dashboard_settings: FlightLogger Business Insights Module (BIM)
    fl_pay:
      module_active: Flightlogger payments is activated on your account.
      info_section: The PAY module is the easiest and most cost-effective way to receive payments from your students, renters & customers directly in FlightLogger. To view transactions as well as to change any settings for FlightLogger Payments please access your BlueSnap account.
      view_bluesnap_account: View Bluesnap Account
      create_bluesnap_account: Create Bluesnap Account
      automatic_payment_info_1: When a user has added a credit or debit card in FlightLogger and have enabled automatic payment this card will automatically be charged whenever a payment request is made on the user through FlightLogger Payments.
      automatic_payment_info_2: By placing a checkmark in “Enforce automatic payment” below, you will enforce users to enable automatic payment when adding a credit or debit card in FlightLogger.
      enforce_automatic_payment: Enforce Automatic Payment
      save: Save
      not_been_activated: <b>FlightLogger Payments (PAY)</b> has not been activated on your FlightLogger platform.
      get_more_info_no_modules: To have a free demo of the modules and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>"><EMAIL></a> or via the support widget in the top navigation bar.
      get_more_info: To get more information on <b>FlightLogger Payments (PAY)</b> or any of our other modules please do not hesitate to contact us by email to <a href="mailto:<EMAIL>"><EMAIL></a> or via the support widget in the top navigation bar.
      start_receiving: To start receiving payments in FlightLogger all you need to do is to activate the module by creating an account with FlightLogger’s payment partner BlueSnap.
      start_receiving_no_modules: To start accepting payments from your students, renters & customers through FlightLogger you need to either have FlightLogger’s User Balance Module (UBM) or an account with QuickBooks and FlightLogger’s QuickBooks Integration Module (QIM).
      adding_with_neither: The PAY module is the easiest and most cost-effective way to receive payments from your students, renters & customers directly in FlightLogger. To learn more about the module and pricing please visit our <a href="https://help.flightlogger.net/knowledge">Help center</a>.
      adding_with_both: The PAY module is the easiest and most cost-effective way to receive payments from your students, renters & customers directly in FlightLogger. Adding this module to your FlightLogger platform will add the ability to receive payment for QuickBooks invoices as well as to top up user balances in FlightLogger.
      adding_with_qb: The PAY module is the easiest and most cost-effective way to receive payments from your students, renters & customers directly in FlightLogger. Adding this module to your FlightLogger platform will add the ability to receive payment for QuickBooks invoices in FlightLogger.
      adding_with_ubm: The PAY module is the easiest and most cost-effective way to receive payments from your students, renters & customers directly in FlightLogger. Adding this module to your FlightLogger platform will add the ability to receive payments and top up the balance of users.
      learn_about_pay: To learn more about the PAY module please visit our <a href="https://help.flightlogger.net/knowledge/getting-started-with-flightlogger-payments">Help center</a>
      learn_about_qb: To learn more about the QIM module please visit our <a href="https://help.flightlogger.net/knowledge/getting-started-quickbooks-integration-module">Help center</a>
      learn_about_ubm: To learn more about the UBM module please visit our <a href="https://help.flightlogger.net/knowledge/getting-started-with-the-user-balance-module">Help center</a>
    ftlm_settings:
      module_activated: The Flight Time Limitation Module (FTLM) is activated on your account.
      helper_text: Settings for the FTLM-module can be found in the "User settings" category.
      module_disabled: |
        The <b>FlightTime Limitation Module (FTLM)</b> has not been activated on your FlightLogger platform.

        To have a free demo of the FTLM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the FTLM module&body=We would like to know more about the FTLM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
    analytics_dashboard_settings:
      module_activated: The FlightLogger Business Insights Module (BIM) has been activated on your account.
      activated:
        widget_name: Widget Name
        type: Type
        paid: Paid (Activated)
        free: Free
      module_trial_activated: The FlightLogger Business Insights Module (BIM) has been activated as a trial on your account. The trial will expire on %{date}.
      module_activate_from_trial: |
        To make sure to keep having access to Business Insights after this date, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the BIM module&body=We would like to know more about the BIM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      module_disabled: |
        The <b>FlightLogger Business Insights Module (BIM)</b> has not been activated on your FlightLogger platform.

        To have a free demo of the BIM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the BIM module&body=We would like to know more about the BIM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
    analytics_dashboard_settings_form:
      widget_drop_down_user_name: User name dropdown
      widget_name_settings: Widget name settings
    sms_settings:
      module_activated: The Safety Management System (SMS) is activated on your account.
      helper_text: Settings for the SMS-module can be found in the "SMS" menu at the top.
      alternative_sms_url: URL or email
      module_disabled: |
        The <b>Safety Management System (SMS)</b> module has not been activated on your FlightLogger platform.

        To have a free demo of the SMS module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the SMS module&body=We would like to know more about the SMS module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      alternative_title: Redirection link
      module_alternative: |
        Despite the SMS is not activated on this account an URL to another SMS or an email address to a Safety Manager can be inserted below.

        Users will then be redirected to the inserted URL or the the email address of the Safety Manager when clicking the "Safety Management" during flight registrations.
    theory_settings:
      title: Ground training pass limits and colors
      description: Set the pass limits on your students' progress tests, theory releases, exams, and type questionnaires, and specify the color to be displayed for below and above the limit
      color_below: Color below
      color_above: Color above
      pass_limit: Pass limit
      save: Save
      theory_exams: Exams
      theory_releases: Theory releases
      progress_tests: Progress tests
      type_questionnaires: Type questionnaires
    report_settings:
      description: Here you can change the default paper size used when exporting reports to PDF format.
      default_report_paper_size: Paper size used when exporting reports to PDF format
      default_receipt_paper_size: Paper size used when exporting receipts
      caa_reference_number: CAA Ref. number of the academy to be used in reports
      save: Save
      export_time_description: |
        <b>Report time formatting</b>
        Set the format of "durations" when exporting reports to CSV/XML format
    sam_settings:
      save: Save
      error: Invalid information. Please correct the inputs below.
      success: Settings saved successfully.
      credit_limit: |
        Below you can insert credit warnings and limits for students and renters.

        • When an account is at or above the credit limit the money icon will be green.
        • When an account is below the credit limit the money icon will be yellow.
        • When an account is below the credit warning the money icon will be magenta.
        • When an account is below zero the money icon will be red.
      linear_discount: Discount value
      description: By placing a checkmark in "Activate discount" below, you will activate the UBM discount feature. This will allow you to insert a general or individual discount number on your students and renters.
      description_instructor_briefing: Below you can insert an hourly instructor briefing/debriefing rate which will be used to charge the student when completing a lesson.
      label_instructor_briefing: Briefing/debriefing rate
      show_accounting: Below you can choose who can see their own accounting information.
      activate_discount_description: |
        By activating discount you will now be able to set an individual discount number on all students and renters. This number will affect new transactions on their student/renter account. You can find and edit the individual students/renters' "discount number" under the students/renters' accounting settings.

        Below you can insert a default discount number, which will be set as a standard when creating a new student/renter. Below are some examples of the usage of the discount number:

          0,8 equals 20% discount
          1,0 equals no discount
          1,2 equals an add-on of 20%
      user_discount_description: |
        Below you can amend this students individual discount number, which will affect the student account. Please find below some example of the usage of the discount number:

          0,8 equals 20% discount
          1,0 equals no discount
          1,2 equals an add-on of 20%
      module_active: The User Balance Module (UBM) is activated on your account.
      module_not_active: |
        The <b>User Balance Module (UBM)</b> has not been activated on your FlightLogger platform.

        To have a free demo of the UBM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the UBM module&body=I would like to know more about the UBM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
    accounting_settings:
      description: Payment for the use of FlightLogger is supported by an automatic payment service, which will deduct your credit card each month. FlightLogger has integrated to the international payment service Stripe. It will require that you add a company credit card. FlightLogger does not store any credit card information.
      invoice_email_description: |
        Each month FlightLogger will send an email including a PDF invoice to the inserted invoice email addresses below.
        The email will tell you when the payment is due. This gives you a chance to review the invoice before the actual charge of your credit card happens.
      invoice_history_description: 'All invoices can be viewed here:'
      add_credit_card: Add credit card
      accepts_invoicing: Activate automatic invoicing
      enable: Enable automatic payment
      automatic_payment_disabled_title: Automatic payment has to be enabled to have an active subscription with FlightLogger
      stripe_information: <b>Stripe information</b>
      invoice_history: "Invoice history"
      save: Save
      invoice_emails: 'Invoice emails:'
    departure_arrival_settings:
      save: Save
      departure_arrival_auth_ack: Choose below settings for authorization and acknowledgement of Departure/Arrival
      departure_arrival_booking_status: Choose below settings for the ability to control the Departure/Arrival's status from the booking page (this only applies for dispatchers)
    booking_settings:
      description: Default timeframe displayed on booking page, daily view (drag the start and end of the slider to adjust)
      milliseconds_hover_effect: Milliseconds before hover effect activates
      booking_email_notification: '"Notify via email" for bookings per default'
      enable: Save
      user_hover_statistics_pre: Student hover statistics
      user_hover_statistics_post: days
      booking_visibility: |
        <b>Booking visibility</b>
        Any booking will remain on the responsible user's front page until it is cancelled or completed. Using the checkboxes below you can choose that a given booking type automatically disappears after X hours. Please note this setting only affects the users which are responsible for cancelling or completing the bookings (Instructors, Crew, Renters).
      specify_single_student_expiry: Specify <b>single student</b> expiry
      specify_multi_student_expiry: Specify <b>multi student</b> expiry
      specify_rental_expiry: Specify <b>rental</b> expiry
      specify_operation_expiry: Specify <b>operation</b> expiry
      specify_class_theory_expiry: Specify <b>class theory</b> expiry
      specify_progress_test_expiry: Specify <b>progress test</b> expiry
      specify_theory_release_expiry: Specify <b>theory release</b> expiry
      specify_theory_exam_expiry: Specify <b>theory exam</b> expiry
      specify_extra_theory_expiry: Specify <b>extra theory</b> expiry
      specify_type_questionnaire_expiry: Specify <b>type questionnaire</b> expiry
      availability_check_on_create_booking: Enable availability warnings when creating bookings
      dep_arr_book: Display Departure/Arrival airport when creating bookings
      display_week_numbers: Display week numbers
      show_sun_phase_indicators: Display twilight and night indicators on booking pages
      sun_phase_indicators_reference_airport: Location (airport)
      use_sun_phase_indicators_fixed_twilight: Enable fixed twilight duration
      sun_phase_indicators_fixed_twilight_minutes_prefix: Fix twilight duration to
      sun_phase_indicators_fixed_twilight_minutes_suffix: minutes
      default_aircraft_booking_type_dropdown: Default aircraft booking type dropdown
      default_theory_booking_type_dropdown: Default theory booking type dropdown
      flash_legit_airport_required: An airport must be selected when twilight and night indicators are enabled.
      booking_title: Booking Preferences
      conflict_warnings_preferences:
        conflict_warning_title: Conflict Warnings Preferences
        certificate_requirement_conflict: Display Certificate Requirement conflicts
        ground_school_requirement_conflict: Display Ground School Requirement conflicts
        flight_and_duty_time_requirement_conflict: Display Flight and Duty Time Requirement conflicts
        user_balance_conflict: Display User Balance conflicts
        maintenance_requirement_conflict: Display Maintenance Requirement conflicts
        squawk_warning_conflict: Display Discrepancy Warning conflicts
        resource_overbooking_conflict: Display Resource Overbooking conflicts
        need_sign_off_conflict: Display Need Sign Off conflicts
        availability_conflict: Display User Availability conflicts
    appearance_settings:
      account_logo:
        title: Logo & aircraft image
        description:
          "Logo is displayed on your FlightLogger login page and within your reports. The logo will also be used as the default aircraft image unless an individual image is uploaded to the aircraft model or the individual aircraft itself.
           (Recommended image resolution: 600x320px)"
        confirm: Are you sure you want to remove the logo and reset to FlightLoggers own?
        submit: Save
        success:
          uploaded: Upload of the file is completed.
          deleted: The file has been deleted and FlightLoggers default formatting will be used again.
        error:
          not_uploaded: The file could not be uploaded.
          not_deleted:
            The file could not be deleted.
      default_profile_image:
        title: Default user image
        description:
          "Image is displayed as the default profile image on all users in your account. \n \t
              (Recommended image resolution: 140x180px)"
        confirm: Are you sure you want to delete the standard profile picture and reset to FlightLoggers own?
        submit: Save
        success:
          uploaded: Upload of the file is completed.
          deleted: The file has been deleted and FlightLoggers default pictures has been inserted.
        error:
          not_uploaded: The file could not be uploaded.
          not_deleted: The file could not be deleted.
      navigation_bar:
        title: Navigation bar colors
        error: An error occured.
        description: Customize the three navigation bar colors on your FlightLogger account. Avoid using white colors for the Top bar color and the Menu bar color, as this will hide the text.
      user_name_formatting:
        title: User name formatting
        dropdown_user_name: Booking page user dropdown
        hover_user_name: Booking page hover dropdown
        popup_user_name: Booking page popup window
        front_page_user_name: Front page widgets
        departure_arrival_user_name: Departure/Arrival
        user_name_format_header: User name formatting
        user_name_format_sub_header: Set how user names are displayed in different areas of FlightLogger.
    general_settings:
      success: Settings saved successfully.
      date_settings:
        date_time_title: |
          <b>Date & time</b>
          Times are as default presented according to the below time zone settings (can be overruled on user level)
        first_day_of_week: First day of the week
        date_format: Date formatting
        time_zone: Account & booking time zone
        flight_time_zone: Flight registration time zone
        theory_time_zone: Theory registration times zone
        show_week_numbers_label: Show week numbers when choosing dates
      users_settings:
        users_title: Users
        guest_users: Enable guest users (can follow the progress of one or more students (view-only). Guest users are charged the same as students)
        contact_on_users: Enable user emergency contact information
        logbook_duration_format: Logbook duration format
        show_active_users: Show if users are active in FlightLogger
        require_reference_uniqueness: |
          <i> The following users conflict with the above settings regarding uniqueness. We advise that you change conflicting reference fields for those users. </i>
        require_call_sign_uniqueness: |
          <b> Note: </b>
          <i> The following users conflict with the above settings regarding uniqueness. We advise that you change conflicting call sign fields for those users. </i>
      registrations_settings:
        registrations_title: Registrations
        duration_format: Duration format
        show_duration_on_lessons: Display duration in lesson briefings
        duration_format_used_when_displaying_reports: Duration format used when displaying reports and student folder
        duration_format_used_when_displaying_maintenance: Duration format used when displaying maintenance
        duration_format_used_when_building_lessons: Duration format used when building lessons
        enable_asymmetric_time: Enable asymmetric time in lessons
        minute_interval: Minute intervals used for flight registration
        minute_interval_note: |
          Note: Changing the minute interval may change the <i>default taxi in</i> and <i>default taxi out</i> settings for your aircraft.
          If an aircraft has enabled taxi time, and the new minute interval doesn't comply with the current <i>default taxi in</i> and <i>default taxi out</i> settings, FlightLogger will automatically adjust those settings to the nearest matching time interval.
        required_title_custom_lesson: Require title when registering custom lesson
        show_extra_action_buttons: Show extra action buttons in flight registration (Status, ETA, POB)
      departure_arrival_settings:
        dep_arr_title: Departure/Arrival
        dep_arr_description: Description
        tracker_provider: Default flight tracker provider
        update_existing_tracker_providers: Enforce new flight tracker URL on all aircrafts
        show_flight_tracking_area: Show "Flight tracking" area during flight registration.
        departure_hours: Amount of hours ahead bookings should be displayed in the departure area
        display_comments: Display comments in Departure/Arrival - dispatcher can still see if unchecked
        comment_from_booking: Departure comments default to booking comment
        duration_format: Departure/Arrival duration format
      acknowledgement_and_authorization:
        title: Acknowledgement and authorization
      email_templates:
        templates_title: Email templates
        auto_emails: Choose and edit the automatic emails that FlightLogger sends out
      save: Save
    cloudsync_integration:
      sign_in_with_provider: "Integrate with %{provider}"
      sign_out_of_provider: "Stop integration with %{provider}"
      are_you_sure: "Are you sure you want to stop the integration with %{provider}?"
      provider_integration_description: "%{provider} integration description"
      description: "Use one the buttons below to start the FlightLogger integration with Dropbox or OneDrive. Once activated data will start to synchronise to your chosen storage provider according to your account settings in FlightLogger."
      alert_onedrive_business_html: "To ensure a safe and stable synchronisation when using OneDrive for Business please follow %{link}."
      alert_only_one_at_a_time: "You can only synchronise to one storage provider at a time. In order to integrate with %{new_service} you must first stop the integration with %{current_service}."
      information_provider_admin: "When your %{provider} account is near it's space limit a warning will be sent via email to the selected Data backup administrators. A user can be made Data backup administrator by amending the user role under edit user."
      below_provider_admin: "Below are the currently selected Data backup administrators:"
      no_provider_administrators: "No users are currently selected as Data backup administrator"
      sync_reports: "By default, the %{provider} integration synchronizes user logbooks, programs, and theory. Below you can select additional reports to synchronize. Each report can be synchronized for a selected period. Daily, weekly, or monthly. If, for instance, weekly is selected then a report will be synced at the start of each week containing a report from the last week."
      enable: Save
      sync_accounting_balances: Accounting balances
      sync_accounting_sales: Account sales
      sync_accounting_transactions: Accounting transactions report
      sync_aircraft_reports: Aircraft reports
      sync_airport_reports: Airport Reports
      sync_booking_statistics: Booking statistics
      sync_cancellation_report: Cancellation report
      sync_fuel_report: Fuel Report
      sync_invoice_report: Invoice report
      sync_production_report: Production report
      sync_raw_data_duty_report: Raw data - duty report
      sync_raw_data_flight_report: Raw data - flight report
      sync_raw_data_theory_report: Raw data - theory report
      sync_account_documents: Sync documents from the document center
      additional_selections_description: "You can also choose to synchronize user certificates and documents:"
      module_active: The Data Backup Module (DBM) is activated on your account.
      module_not_active: |
        The <b>Data Backup Module (DBM)</b> has not been activated on your FlightLogger platform.

        To have a free demo of the DBM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the DBM module&body=We would like to know more about the DBM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name: %0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation: "><EMAIL></a> or via the support widget in the top navigation bar.
    user_settings:
      enable: Save
      administrator: Administrator
      administrators: Administrators
      instructor: Instructor
      instructors: Instructors
      ground_instructor: Ground instructor
      ground_instructors: Ground instructors
      flight_instructor: Flight instructor
      flight_instructors: Flight instructors
      crew: Crew
      student: Student
      students: Students
      staff: Staff
      renter: Renter
      renters: Renters
      permission_sub_header: "FlightLogger is currently improving permissions control and some settings have already moved to the new User permissions area. Head on over <a href='%{permissions_path}'>here</a> to change permissions across user roles."
      general_description: "General settings"
      guest_description: "By placing a checkmark in \"Enable guest users\" below, you will activate the administrators right to create guest users. These users can be created under Users->Guests and will be able to follow the progress of one or multiple students, in a view-only mode. Note: Guest users will be charged the same price as active students."
      allow_auto_grades_description: "By placing a checkmark below, you will allow instructors to auto fill all exercises on a lesson with a specific grade. Note: Each grade will have to have been setup by FlightLogger to allow for autofill. Contact FlightLogger if you need this for some grades."
      warning_overview:
        description: "Warnings"
        type_labels:
          maintenance: "Access to maintenance warning overview"
          certificate: "Access to certificate warning overview"
          ground_school: "Access to ground training warning overview"
          flight_time_limitation: "Access to flight and duty time warning overview"
          squawks: Access to discrepancies warning overview
      booking_overview:
        description: "Bookings"
        type_labels:
          pages: "Can see booking pages"
          details: "Can see booking details"
          availabilities: "Can see booking availabilities"
          cancelled: "Can see cancelled bookings"
      activities_overview:
        description: "Activities"
        instructors_can_see_other_instructors_activities: "Instructors can see other instructors' activities and bookings missing completion"
      availabilities_overview:
        description: "Availabilities"
        instructors: Instructor
        crew: Crew
        students: Student
        type_labels:
          availabilities_enabled: "Availabilities enabled for"
          edit_own_availabilities: "Can edit own availabilities"
      duty_time_overview:
        description: "Duty time"
        type_labels:
          enabled: "Duty time enabled for"
          edit: "Can edit own duty time"
          display: "Display users on duty"
          invoice: "Invoice users on duty"
        administrator_duty_time: Enable duty time on administrators
        administrator_can_edit_own_duty_time: Administrators can edit their own duty time
        administrator_show_on_duty: Show which administrators are currently on duty
        instructor_duty_time: Enable duty time on instructors
        instructor_can_edit_own_duty_time: instructors can edit their own duty time
        instructor_show_on_duty: Show which instructors are currently on duty
        crew_duty_time: Enable duty time on crew
        crew_can_edit_own_duty_time: Crew can edit their own duty time
        crew_show_on_duty: Show which crew are currently on duty
        staff_duty_time: Enable duty time on staff
        staff_can_edit_own_duty_time: Staff can edit their own duty time
        staff_show_on_duty: Show which staff are currently on duty
        student_duty_time: Enable duty time on students
        student_can_edit_own_duty_time: Students can edit their own duty time
        student_show_on_duty: Show which students are currently on duty
        renter_duty_time: Enable duty time on renters
        renter_can_edit_own_duty_time: Renters can edit their own duty time
        renter_show_on_duty: Show which renters are currently on duty
      instructor_permissions_overview:
        description: "Instructor permissions"
        manage_approved_lesson: "Instructors can change lessons approved by students"
        manage_student_state: "Instructors can create and change status of students"
        manage_class_state: "Instructors can create and change status of classes"
        manage_exams: "Instructors can edit/delete exams"
      crew_staff_permissions_overview:
        description: "Crew & staff permissions"
      renter_student_permissions_overview:
        description: "Renter & student permissions"
        renter_registrations: Renters can add/edit/delete rental registrations
        change_call_sign: Renters and students can edit their own call sign
        change_name: Renters and students can edit their own First name & Last name
        user_documents: Renters and students can add/edit/delete their own documents
        students_can_request_bookings: Students can request bookings
        edit_avatar: Renters and students can edit their own image
        edit_info: Renters and students can edit their own info
        aircraft_status: Renters and students can see aircraft status
        message_students: Students can message other students via the message center
        message_instructors: Students can message instructors via the message center
        contact_when_approving: Students can contact instructor via message when approving lessons
      user_privacy_overview:
        description: "User privacy"
        allow_profiles_all: "Allow crew, staff, and instructors access to these roles"
        allow_profiles_students: "Allow students access to these roles (Instructors only)"
        remove_staff_privacy: Staff
        remove_crew_privacy: Crew
        remove_instructor_privacy: Instructors
        remove_instructor_privacy_for_students: Instructors
      miscellaneous_overview:
        description: "Miscellaneous"
        type_labels:
          approve_certificates: "Can approve own certificates (Certificate administrators only)"
          control_wall_posts: "Can add/edit/delete wall posts"
        admin_certificates: Administrator can approve their own certificates
        instructor_certificates: Instructor can approve their own certificates
        crew_certificates: Crew can approve their own certificates
        staff_certificates: Staff can approve their own certificates
        instructor_wall_posts: Instructors can add/edit/delete wall post
        crew_wall_posts: Crew can add/edit/delete wall post
        staff_wall_posts: Staff can add/edit/delete wall post
      conflicts_overview:
        description: "Conflicts"
        duplicate_user_call_signs: Require uniqueness of user call sign field
        duplicate_user_references: Require uniqueness of user reference field
      user_security_overview:
        allow_profiles_all: "Enforce 2-Factor Authentication"
        description: 'User security'
        add_admin_mfa: "Administrators"
        add_instructor_mfa: "Instructors"
        add_crew_mfa: "Crew"
        add_staff_mfa: "Staff"
        add_renter_mfa: "Renters"
        add_student_mfa: "Students"
        add_guest_mfa: "Guests"
        disabled_title: "To change settings enable 2-Factor Authentication on your my|FlightLogger page"
    flight_time_limitation_settings:
      module_not_active: |
        The <b>Flight Time Limitation Module (FTLM)</b> has not been activated on your FlightLogger platform and therefore you don't have access to duty time functionality.

        To have a free demo of the FTLM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the FTLM module&body=We would like to know more about the FTLM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      description: Choose below which users group have duty time enabled and if they can edit their own duty time.
      enable: Save
      expenses_description: "Choose below if registered duty time counts towards invoice report - expenses, for the following user groups"
    gdpr_settings:
      article_link_text: FlightLogger Help Center.
      description: "Contact <a href='mailto:<EMAIL>'><EMAIL></a> in order to choose/change your preferred Data Protection Officer (DPO). The selected DPO will be able to 'GDPR neutralize' user(s) in FlightLogger. More info about GDPR neutralization can be found in the %{article}"
      enable: Save
    user_roles_settings:
      enable: Save
      general_description:
      feedback_receivers_title: Feedback receiver(s)
      feedback_receivers_description: Receives message center feedback messages
      maintenance_receivers_title: Maintenance receiver(s)
      maintenance_receivers_description: Receives message center maintenance messages
      squawks_receivers_title: Discrepancy warning receiver(s)
      squawks_receivers_description: Receives notification in case a new discrepancy is reported on an aircraft
      eta_receivers_title: ETA warning receiver(s)
      eta_receivers_description: Receives notification in case an aircraft has not arrived according to ETA
      fuel_receivers_title: Fuel endurance warning receiver(s)
      fuel_receivers_description: Receives notification in case an aircraft has not arrived within stated fuel endurance
      master_list_title: Master exercise list admin(s)
      master_list_description: Can make changes to master exercise lists
      cbta_admins_title: CBTA admin(s)
      cbta_admins_description: Can make changes to flight phases, CBTA color ranges, core competencies, and performance indicators
      cbta_module_not_active: |
        FlightLogger CBTA-Pro is not activated on your account. Please write to <a href="mailto:<EMAIL>?subject=Please contact me for more info about FlightLogger CBTA-Pro&body=I am interested in knowing more about FlightLogger CBTA-Pro. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> to get a free online demo of FlightLogger CBTA-Pro.
      sms_title: Safety manager
      sms_description: Receives safety management reports and has access to SMS reports, analysis, statistics, and administration
      alternative_sms_url: URL or email
      sms_module_not_active: |
        The SMS module is not activated on your account. Please write to <a href="mailto:<EMAIL>?subject=Please enable the SMS module on our account&body=We would like to enable the SMS module on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a>  if you wish to activate the SMS module.
      article_link_text: FlightLogger Help Center.
      dpo_title: Data protection officer (DPO)
      dpo_description: Note that a DPO can only be changed or undone by contacting FlightLogger support.
      student_settings_description: By default students can only send feedback and maintenance messages. Here you can choose to enable normal messages for students for certain groups.
      collaboration_admins_title: "Collaboration admin(s)"
      collaboration_admins_description: "Receives notifications in case another FlightLogger organization requests collaboration. Can also manage pending collaboration requests as well as all existing collaborations."
      fuel_notification_delay: Send fuel endurance warning after
      fuel_notification_secondary_delay: Enable second fuel endurance warning after
      eta_notification_delay: Send ETA warning after
      eta_notification_secondary_delay: Enable second ETA warning after
      minutes: minutes
    cbta_settings:
      module_active: FlightLogger CBTA-Pro is activated on your account.
      module_not_active: |
        The <b>Competency-Based Training and Assessment (CBTA-Pro)</b> module has not been activated on your FlightLogger platform.

        To have a free demo of the CBTA-Pro module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about FlightLogger CBTA-Pro&body=We would like to know more about FlightLogger CBTA-Pro to possible activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      grading_title: Grading settings
      default_norm: |
        Default norm
        (used as norm on new exercises when creating lessons on a CBTA revision)
      grade_norm_by_default: Default grade with norm (all exercises are graded with norm grade when starting a lesson registration)
      allow_norm_extra_exercises: Allow instructors to define norm when adding extra exercises
      can_grade_category_to_norm: Instructors can grade an entire flight phase to norm during lesson registration
      default_lesson_buttons: Lesson buttons
      default_selected: Default selected
      matrix_title: CBTA tab
      matrix_description: 'Default values displayed on matrix in the CBTA tab for tracking student performance ("track number of lessons back in time") - insert preferred values:'
      matrix_custom: Custom
      matrix_all: All
      save: Save
    no_change_file: "No change in file"
    quickbooks_settings:
      description: "The QuickBooks integration enables you to create an invoice in QuickBooks after each flight.
      When you connect your QuickBooks account with FlightLogger, we will start pulling your customer and items data from QuickBooks into FlightLogger.
      Using this data, we can prefill your invoices with the relevant customers and items."
      save: "save"
      reintegrate: "Re-integrate to QuickBooks"
      integrate: "Integrate with QuickBooks"
      disintegrate: "Stop integration with QuickBooks"
      quickbooks_message: "QuickBooks is great .. bla bla bla..."
      remove_warning: "Are you sure you want to stop the integration with QuickBooks?"
      module_not_active: |
        The <b>QuickBooks Integration Module (QIM)</b> has not been activated on your FlightLogger platform.

        To have a free demo of the QIM module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about FlightLogger QIM&body=We would like to know more about FlightLogger QIM to possible activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
    flight_tracker_settings:
      link_to_dep_arr_title: "Choose below your preferred flight track provider and connect using the flight track URL for each aircraft to enable live tracking."
      airnav_header: "AirNav RadarBox"
      spidertracks_header: "Spidertracks"
      other_providers_header: 'Other providers'
      flight_aware: 'FlightAware'
      flight_radar: 'Flightradar24'
    flight_tracker_aircraft_table_section:
      airnav: "AirNav RadarBox"
      spidertracks: "Spidertracks"
      flight_aware: 'FlightAware'
      flight_radar: 'Flightradar24'
      empty: ""
    edit: "Edit"
    user_settings_overviews:
      user_security_overview:
        explain_mfa: "To change any of the security settings below, please activate 2-Factor Authentication in your"
        settings: "settings."
  aci_invoice_recharge_templates:
    recharge_templates: Recharge Templates
    create_recharge_template: Create Recharge Template
    edit_recharge_template: Edit Recharge Template
  aircraft_program_prices:
    create_label: Aircraft program price was created
    destroy_label: Aircraft program price was deleted
    edit:
      title: 'Update aircraft price for: %{plane_callsign}'
      submit_btn: Update aircraft price
    form:
      advance_pricing: Advanced pricing
      btn_create: Save price
      btn_edit: Save price
      btn_new: Save price
      btn_update: Save price
      is_it_rental: Rental price
      vfr: VFR
      ifr: IFR
      dual: DUAL
      solo: SOLO
      spic: SPIC
      hour_rates: Hourly rates
    form_advance_pricing:
      vfr: VFR hourly rate
      ifr: IFR hourly rate
      dual: DUAL
      solo: SOLO
      spic: SPIC
      hour_rates: Hourly rates
    form_advance_pricing_simulator:
      vfr: VFR hourly rate
      ifr: IFR hourly rate
      dual: DUAL
      solo: SOLO
      spic: SPIC
      hour_rates: Hourly rates
    hour_rate: Hourly rate
    index:
      title: 'Aircraft prices for: %{plane_callsign}'
      create: 'Create aircraft price'
      no_log_for_aircraft: 'Flight log not present on aircraft'
    new:
      title: 'New aircraft price for: %{plane_callsign}'
      submit_btn: Create aircraft price
    new_btn: Add price to program
    program: Program
    advanced_pricing_enabled: Advanced pricing enabled
    log_number: Flight log
    empty_program: There are no prices yet
    update_label: Aircraft program price was updated
    aircraft_program_price:
      confirm_delete_price: Are you sure, that you want to delete the price?

  airports:
    airport:
      deactivate: "Are you sure, that you want to disable the airport: %{airport_name}?"
      all_airports: ALL AIRPORTS
    index:
      active: Active
      deactivated: Deactivated
      name: Name
      legit_airport: Connected Airport
      no_active_airport: There are no active airports to be shown at the moment
      no_deactivated_airport: There are no deactivated airports to be shown at the moment
      old_airports: Deactivated airports
      title: Airports
    report:
      title: Airports
      name: Name
    form:
      fields:
        legit_airport_html: Airport Code
  application:
    account_disabled: The account has been disabled.
    access_denied: You do not have the required permission to access this page.
    has_not_payed: This FlightLogger account will no longer be active from %{shutdown_date}. Contact the administration.
    is_shutdown: This FlightLogger organization is currently suspended due to lack of payment, please contact the administration.
    update_info: You have to update your information before you can use FlightLogger.
    update_password: You have to update your password because of security measures
    invalid_date: Invalid date choosen, todays day is selected instead
    account_does_not_exist: This account does not exist.
    missing_record: The requested %{model} could not be found! Maybe it has been moved or deleted.
  avatars:
    form:
      update_image: Update image
      delete_image: Delete current image
    edit:
      preview: This is how your image looks
      title: Edit image
    flash_no_file: Please select a file to upload
    flash_created: Profile picture updated
    flash_updated: Profile picture updated
    flash_no_change: No change in profile picture
    flash_deleted: Image has been deleted
    new:
      preview: This is how your image looks
      title: Edit image
  plane_avatars:
    form:
      update_image: Update image
      delete_image: Delete current image
    edit:
      preview: This is how your image looks
      title: Edit image
    flash_no_file: Please select a file to upload
    flash_created: Profile picture updated
    flash_updated: Profile picture updated
    new:
      preview: This is how your image looks
      title: Edit image
  backend:
    dashboard:
      bookings_empty: There are no bookings yet
      my_bookings: My bookings
    unapproved_user_lectures:
      see_unapproved_lecture: See lesson and approve
      unapproved_lectures: Requiring approval
  bookings_choosers:
    show:
      no_booking_detected: Select a booking for your registration
      continue_no_booking: Continue without a booking
      signed_in_as_admin: As an administrator you can switch between %{types} to select the correct booking
    bookings_list:
      choose_booking: Proceed
  booking_activities:
    form:
      no_booking: No booking matches
  booking_report:
    report_for: Booking statistics from %{from} - %{to}
    aircrafts:
      aircrafts: Aircraft
      block_time: Flight
      bookings_total: Bookings total
      cancelled: Cancelled
      completed: Completed
      name: Name
      avg_preflight: Briefing
      avg_postflight: Debriefing
      no_aircraft_bookings: "There are no statistics yet"
    classrooms:
      bookings_total: Bookings total
      cancelled: Cancelled
      classrooms: Classrooms
      completed: Completed
      name: Name
    instructors:
      block_time: Flight
      bookings_total: Bookings total
      cancelled: Cancelled
      completed: Completed
      instructors_practical: Instructor, Crew and Renter
      name: Name
      preflight: Briefing
      postflight: Debriefing
      no_instructors_practical_bookings: "There are no statistics yet"
      total: Total
    show:
      aircrafts: Aircraft
      booking_report: Booking statistics
      classrooms: Classrooms
      find_report: Find report
      instructors: Instructor, Crew and Renter
    students:
      average_delay: Delay
      coverage: Coverage
      name: Name
      students: Students
  bookings:
    approved: Approved
    awaiting_approval: Awaiting approval
    shared:
      registration_without_booking_not_allowed: "Registration without a booking is not allowed"
    booking_footer:
      icon_description: "Icon description:"
      user_certificate_status: "Shows the user's certificate status"
      user_need_sign_off_message_status: "Shows the user's need sign off message status"
      user_availability_status: "Shows the user's availability status"
      user_flight_time_limitation_status: "Shows the user's flight and duty time limitation status"
      ground_school_warning_status: "Shows the user's ground school warning status"
      user_account_status: "Shows the user's account status"
      aircraft_maintenance_status: "Shows the aircraft maintenance status"
      cancelled_by: Cancelled %{at} by %{by}
      completed_by: Completed %{at} by %{by}
      updated_by: Booking updated %{at} by %{by}
      updated_at: Updated %{at}
      by: "%{at}"
    disabled_student: This user is not active. Please activate using the link next to this icon. Update/proceed buttons are only available when all users selected are active.
    choose_file: Choose file
    no_file: No file chosen
    all:
      instructor_times: Instructor times
      student_times: Student times
    booking:
      delete_booking: Delete rental
    booking_data:
      lesson: 'Lesson'
      total_flights: 'Total Flights:'
      status: 'Status: Completed'
      lesson_booked: 'Lesson booked:'
      lesson_completed: 'Lesson completed:'
      airborne: 'Total Airborne:'
      block: 'Total Block:'
      tacho: 'Total Timer:'
      departure: 'Departure:'
      arrival: 'Arrival:'
      comment: Comment
      complete_lesson: Proceed lesson
      edit_lesson: Edit lesson
      from: From
      instructor: Instructor
      pic: PIC
      amount_of_crew: crew members in registration
      crew: Crew
      plane: Aircraft
      participants: Participants
      renter: Renter
      attended: "Attended:"
      didnt_attend: "Didn't attend:"
      partial_attend: "Attended partially:"
      student_count: Student %{count}
      student: Student
      pass: "Passed:"
      fail: "Failed:"
      duration: "Duration:"
      students: Students
      subject: Lesson
      to: To
      customer: Customer
      observers: Observer
      classroom: Classroom
    cancelled_booking:
      cancel_reason: Cancellation reason
      cancel_comment: Cancellation comment
      status: "Status: Cancelled"
      departure_arrival: Departure/Arrival
    booking_documents_form:
      add_booking_document: Add a document
    booking_element:
      cancel: Cancel
      complete: Proceed
      complete_number: Proceed %{number}
      cancel_number: Cancel %{number}
      complete_lesson: Proceed
      edit_lesson: Edit lesoon
      see_more: ...
      24h_in_future: "The booking is more than 24 hours in the future"
      days_later:
        one: "%{count} day later"
        other: "%{count} days later"
    booking_element_body:
      classroom: 'Classroom'
      comment: 'Comment'
      departure: 'Departure/Arrival'
      document: 'Document'
      subject: 'Subject'
      plane: 'Aircraft'
    booking_element_users:
      customer: Customer
      participants: Participants
      pic: PIC
      renter: Renter
      student: Student
      students: 'Students'
      students_number: "Student %{number}"
      instructor: 'Instructor'
      crew: "Crew"
      observers: "Observer"
      class: Class
    popup_user_lecture_info:
      lesson: Lesson
    booking_document:
      document: Document
    cancelled:
      cancelled_bookings: Cancelled bookings
      comment: Comment
      date: Date
      instructor: Instructor
      person_for_user: Instructor
      person_for_instructor: Student/Class/Renter/Customer
      person_for_plane: Instructor/Crew/Renter
      no_canceled_lectures: No cancelled bookings
      reason: Reason
      type: Type
    date_switcher:
      all: All
      planes: Aircraft
      students: Students
      today: Today
      show_cancelled_bookings: Click here to show all cancellations
      and_crew: '& crew'
      filter: Filter
      stay_open: Stay open
    flash_cancelled: Booking cancelled
    flash_destroyed: The booking has been deleted and the renter has been notified
    get_unbooked_lessons:
      student: Student
      lessons: Lessons
      not_flown_lessons: There exists lessons which is not flown, to the left of the lessons you can see.
      credited: CREDITED
      no_lessons_in_program: There are no lessons in this program.
    index:
      new_booking: New booking
      from: From
      to: To
      comment: Comment
    personnel:
      student: Student %{count}
      instructor: Instructor
      pic: PIC
      crew: Crew
      renter: Renter
      observer: Observer
    open:
      bookings: Bookings
      empty: There are no bookings yet
    open_bookings:
      bookings_empty: There are no bookings yet
      my_bookings: My bookings
    students: Students
    student: Student
    unbooked_lessons:
      tip: Hold down shift in order to create a multi student booking.
      choose_class_or_program: Search for Students, Classes or Flight Training Programs
      window_title: Student overview
    lesson_selector:
      please_select_lesson: "Please select a lesson"
    booking_time:
      start: "Start"
      end: "End"
    show:
      notify_via_email: Notify via email
      enabled: Enabled
      disabled: Disabled
      last_updated: "Last update: %{updated_at} By: %{updated_by}"
      aircraft_squawk_warning: Aircraft discrepancy warning status
      user_accounting_warning: Accounting warning status
      user_ground_training_warning: Ground training warning status
      user_availability_warning: Availability status
      user_duty_time_warning: Duty time warning status
      user_sign_off_warning: Sign off message status
      user_certificate_warning: Certificate status
      aircraft_maintenance_warning: Aircraft maintenance warning status
    approval_modal:
      modal_header: Approve booking
  bookings_mailer:
    should_use_frontpage: NB. We advise you to always verify your bookings on FlightLoggers front page to get the most accurate picture of your bookings situation, alternatively FlightLogger offers a calendar sync functionality
    booking_info:
      comment: Comment
      start: Start
      plane: Aircraft
      end: End
      instructor: Instructor
      classroom: Classroom
      participant: Participant(s)
      activity: Operation
      crew: Crew
      pic: PIC
      renter: Renter
      student: Student(s)
      observer: Observer
      cancellation_info:
        header: "Info about your cancelled booking:"
        rental_header: "Info about your cancelled rental:"
        reason: "Reason: %{reason}"
        comment: "Comment: %{comment}"
  bookings_recurring_mailer:
    should_use_frontpage: NB. We advise you to always verify your bookings on FlightLoggers front page to get the most accurate picture of your bookings situation, alternatively FlightLogger offers a calendar sync functionality
    booking_info:
      comment: Comment
      start: Start
      plane: Aircraft
      end: End
      instructor: Instructor
      classroom: Classroom
      participant: Participant(s)
      activity: Operation
      crew: Crew
      pic: PIC
      renter: Renter
      student: Student(s)
      observer: Observer
      cancellation_info:
        header: "Info about your cancelled booking:"
        rental_header: "Info about your cancelled rental:"
        reason: "Reason: %{reason}"
        comment: "Comment: %{comment}"
  canceled_lectures:
    flash_created: The canceled lesson is created
    flash_destroyed: The lecture is deleted
    new:
      title: Title
  cancellation_reasons:
    cancellation_reason:
      deactivate: "Are you sure, that you want to deactivate cancellation reason: %{reason_name}?"
    index:
      active: Active
      deactivated: Deactivated
      disabled: Deactivated Cancellation reasons
      title: Cancellation reasons
      name: Name
      no_active_cr: There are no active cancellation reasons to be shown at the moment
      no_deactivated_cr: There are no deactivated cancellation reasons to be shown at the moment
      create_cancellation_reason: Create cancellation reason
    form:
      description: "Appears when cancelling bookings of type:"
      create_cancellation_reason: Create cancellation reason
      theory: Theory
      flight: Flight
    old:
      title: Deactivated Cancellation reasons
      name: Name
      enabled: Active Cancellation reasons
  cancelled_bookings:
    index:
      cancelled_bookings: Cancelled bookings
      date: Date
      instructor: Instructor
      type: Type
      reason: Reason
      comment: Comment
      list_empty: "There are no cancelled bookings yet"
    flash_created: The cancelled booking has been created
    instructor_invoice_form:
      save: Save
    invoice_form:
      save: Save
    new:
      create: Cancel booking
      cancelled_booking_for: Booking cancellation for %{user_name}
      title_with_user: Booking cancellation (%{user_name})
      title: Booking cancellation

  cancelled_booking_report:
    date: Date
    off_block: Start
    planned_hours: Planned Hours
    booking_type: Booking type
    activity: Activity
    reason: Reason
    resource: Resource
    pilot: Pilot
    student: Student/Class/Renter/Customer
    comment: Comment
    report:
      total_planned_hours: "Total"
      empty_cancellations: "There are no cancellations yet"
  categories:
    form:
      save: Create category
      update: Update category
    edit:
      title: Edit category
    new:
      title: Create new category
  certificate:
    to_approval:
      no_certificates: No certificates requiring approval.
    current:
      no_certificates: There are no current certificates.
    previous:
      no_certificates: There are no previous certificates.
    shared:
      to_approval: Requiring approval
      expiry: Expiry
      approved: Approved
      by: by
      uploaded: Uploaded
      confirm_reject: Are you sure you want to reject this certificate?
      confirm_reject_maintenance: Are you sure you want to reject this maintenance?
      renew: Renew
      current_certificates: Current certificates
      previous_certificates: Previous certificates
      creation_date: Creation date
      reject: Reject
      edit: Edit
      name: Name
      reason: Reason
      unapproved_and_expired_certificates: Rejected or expired certificates
      no_certificates: There are no certificates yet
    level_must_meet_requirement: "Level must meet the certificate requirement specification"
    reply_title: "Regarding %{status} %{certificate_type}"
  certificate_presenter:
    renewed: Renewed
    rejected: Rejected
    expiration_reached: Expired
  renew_certificates:
    show:
      title: Renew certificate
  certificates:
    certificate_approved: Certificate approved
    certificate_unapproved: Certificate rejected
    edit:
      title: Update certificate
    flash_created: The certificate is created
    flash_updated: The certificate is updated
    fields:
      no_file: No file chosen
      choose_file: Upload certificate
      class: Class
      level: Level
    form:
      confirm_reject: Are you sure you want to reject the certificate?
      reject: Reject certificate
      submit: Save certificate
    new:
      title: Create Certificate
  checkins:
    table_header:
      aircraft: AIRCRAFT
      departure: DEP
      type: TYPE
      student_renter: STU/REN/CUS
      instructor_crew: INS/CREW
      observers: OBSERVERS
      pob: PoB
      fuel: FUEL
      arrival: ARR
      comment: COMMENT
      status: STATUS
    presenter:
      operation: Operation
      school_flight: School flight
      no_type_name_found: No type name found'
  classrooms:
    name: Name
    classroom:
      deactivate: "Are you sure that you want to deactivate classroom: %{classroom_name}?"
    index:
      active: Active
      deactivated: Deactivated
      name: Name
      classrooms: Classrooms
      old_classrooms: Deactivated classrooms
    old:
      active_classrooms: Active classrooms
      deactivated_classrooms: Deactivated classrooms
    calendar:
      calendar_synchronization: Calendar synchronization
      calendar_explaination: Using the link below you can synchronize bookings of this classroom to your calendar. You can revoke access by clicking renew which will generate a new link for calendar synchronization. The previous link will cease to work when a new link is generated.
      renew: Renew
      renew_explaination: Renew calendar link
      guides_info: The video guides below will guide you through the set up of calendar synchronization with different calendars. You can ignore the first few seconds as they refer to synchronization of your personal calendar feed.
  booking_filters:
    class: Class
    current_airport: Current airport
    current: "%{name} (current)"
    home_airport: Home airport
    home: "%{name} (home)"
    maintenance: Maintenance
    maintenance_up_to_date: Maintenance current
    type: Type
    airplane: Airplane
    helicoptor: Helicoptor
    callsign: Call sign
    certificates: Certificates
    certificates_up_to_date: Certificates current
    roles: Roles
    name: Name
    classes: Classes
    programs: Programs
    general: General
    all_active: All active
    availabilities: Availabilities
    flight_time_limitation: Flight time limitation
    flight_time_limitation_current: Flight time current
    available: Available
    error_message: "Unable to %{action} filter!"
    index:
      booking_filters: Booking - common filters
      resource_occupation: Resource - Availability settings
    booking_filter:
      and_crew: '& crew'
      classrooms: Classrooms
      filter: Filter
      filter_name: Filter name
      save_personal_filter: Save personal filter
      save_common_filter: Save filter
      cancel_edit_filter: Cancel edit filter
      planes: Aircraft
      students: Students
      default_error_message: "Error!"
      edit_hover: Edit filter
      delete_hover: Delete filter
    availability_settings:
      levels_of_availability: Levels of availability
      high: HIGH
      medium: MEDIUM
      low: LOW
      high_text: Booked less than
      low_text: Booked more than
      save_settings: Save settings
  customers:
    active: Active
    deactivated: Deactivated
    new_customer: Create Customer
    active_customers: Active customers
    deactivated_customers: Deactivated customers
    no_active_customers: There are no active customers to be shown at the moment.
    no_deactivated_customers: There are no deactivated customers to be shown at the moment.
    deactivate: "Are you sure, that you would like to deactivate customer: %{customer_name}?"
    edit_btn: Edit
    customers: Customers
    flash_merge: The customers has been merged
    flash_merge_error: You have to choose a customers to be merged.
    report:
      date: Date
      dual: Dual
      invoice: Invoice
      invoice_marked: Invoice
      invoice_number: Invoice number
      mark_all: Mark all
      sim: SIM
      solo: Solo
      spic: SPIC
      update: Edit
      what_is_the_invoice_number: What is the invoice number
    show:
      activities: Operations
      plane: Aircraft
      activity: Operation
      booking: Booking
      bookings: Bookings
      cancelled_bookings: Cancelled bookings
      confirm_activity_destroy: Are you sure you want to delete the operation
      documents: Documents
      date: Date
      delete: Delete
      edit_customer: Edit customer
      invoice_number: Invoice number
      merge: Merge
      merge_customer: Merge customers
      merge_help: Choose which customer the current one shall be merged with.
      merge_the_customer: Merge customers
      pic: PIC
      reports: Reports
      settings: Settings
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      update_image: Update image
      confirm_delete: Are you sure you want to delete the operation?
      user_accountings:
        title: Accounting
        invoices: Invoices
        payments: Payments
  customer_avatars:
    form:
      update_image: Update image
      delete_image: Delete current image
    edit:
      preview: This is how your image looks
      title: Edit image
    flash_no_file: Please select a file to upload
    flash_created: Profile picture updated
    flash_updated: Profile picture updated
    new:
      preview: This is how your image looks
      title: Edit image
  customer_documents:
    breadcrumbs:
      documents: Documents
    edit:
      title: Edit document
    flash_created: The document has been uploaded
    flash_destroyed: The document has been deleted
    flash_updated: The document has been updated
    folders:
      confirm: Are you sure you want to delete the folder
    documents:
      confirm: "Are you sure you want to delete this document?\nThis can not be undone!"
      no_documents: There are no documents yet
    index:
      confirm: Are you sure you want to delete this document
      delete: Delete
      documents: Documents
      name: Name
      new_document: Upload documents
      new_folder: New folder
      update: Edit
      modified: Modified
      no_documents: There are no documents yet
    new:
      title: Upload new document
  customer_document_folders:
    edit:
      title: Edit folder
      no_file: No file chosen
      choose_file: Choose file
    flash_cant_destroy: The folder can't be deleted, because it contains files.
    flash_created: The folder is created
    flash_destroyed: The folder is deleted
    flash_updated: The folder is updated
    new:
      title: Create folder
      no_file: No file chosen
      choose_file: Choose file
    show:
      confirm_delete: Are you sure you want to delete this folder?
      delete: Delete
      documents_in: 'Documents in '
      name: Name
      members: Members
      update: Edit
  date:
    day_names:
      - Sunday
      - Monday
      - Tuesday
      - Wednesday
      - Thursday
      - Friday
      - Saturday
  document_folders:
    edit:
      title: Edit folder
      no_file: No file chosen
      choose_file: Choose file
    flash_cant_destroy: The folder can't be deleted, because it contains files.
    flash_created: The folder is created
    flash_destroyed: The folder is deleted
    flash_updated: The folder is updated
    new:
      title: Create folder
      no_file: No file chosen
      choose_file: Choose file
    show:
      confirm_delete: "Are you sure you want to delete this folder?\nThis can not be undone!"
      confirm_delete_all: "Please note that this folder has content which will also be deleted.\n\nAre you sure you want to delete this folder?\nThis can not be undone!"
      delete: Delete
      documents_in: 'Documents in '
      name: Name
      members: Members
      modified: Modified
      update: Edit
  documents:
    breadcrumbs:
      documents: Documents
    documents:
      shared_with: '%{count} members'
      inherited_sharing: "Sharing inherited via %{model}"
      no_documents: There are no documents yet
    edit:
      title: Edit document
    flash_created: The documents has been uploaded
    flash_destroyed: The document has been deleted
    flash_updated: The document has been updated
    form:
      can_be_seen: "Can be seen by:"
      no_file: No file chosen
      choose_file: Choose file
      create: "Upload documents"
      update: "Update document"
      use_extension: "Use file extension in name"
    folders:
      shared_with: '%{count} members'
    index:
      confirm_document: "Are you sure you want to delete this document?\nThis can not be undone!"
      create_new: New folder
      delete: Delete
      documents: Documents
      name: Name
      members: Members
      modified: Modified
      update: Edit
      upload_new: Upload documents
    search:
      search: Search
    search_result:
      documents: Documents
      name: Name
      folder: Folder
      members: Members
      modified: Modified
    new:
      title: Upload documents
    flash_no_document: "No documents selected!"
  document_mover:
    index:
      root: Documents
      guide: 'Choose a folder to move "%{moveable}" into'
    flash_moved: '"%{moveable}" has been moved to "%{folder}"'
    flash_moved_root: '"%{moveable}" has been moved to "Documents"'
    flash_failed_to_move: 'Failed to moved folder!'
  user_can_watch:
    watching:
      header: Access restrictions for %{user_name}
      category: Category
      name: Name
      roles: Roles
      user: User
      empty_restriction: There are no restrictions yet
      is_watching: 'User access list - %{user_name} has access to the following users'
      type:
        group: Groups
        user_type: User types
        team: Classes
        program: Programs
        user: Users
      missing_resource: '%{watchable_type} resource has been deleted!'
    index:
      description: |
        The user access list shows which users %{user_name} has access to in FlightLogger. By selecting groups and/or individual users below you can restrict the user access list.
        Note that some selections may be overridden in the account settings and therefore will not effect the user access list.
      done: Done
      name: Name
      share_btn: Share
      share_with: 'Share with:'
      watchable_label: Users
      submit: Add
      title: Restrict user access
      title_guest: Guest user access
  document_shares:
    shared_with:
      name: Name
      currently_shared_with: '%{shareable_type} is currently shared with'
      roles: Roles
      inherited: Inherited
      shareable_type:
        document_folder: folder
        document: document
      type:
        group: Groups
        user_type: User types
        team: Classes
        program: Programs
        user: Users
      missing_resource: '%{shared_with_type} resource has been deleted!'
      role:
        no_access: No access
        no_access_description: Member have no view or edit rights
        read_access: Can view
        read_access_description: Member can view only
        write_access: Can edit
        write_access_description: Member can edit, delete and add files
        manage_access: Can manage
        manage_access_description: Member can edit, delete and add files and manage sharing rights
    index:
      done: Done
      name: Name
      share_btn: Share
      share_with: 'Share with:'
      title: Share
  emergency_contect_explanation: Please ensure that the person you nominate above
    as being your emergency contact has provided their consent to you providing us
    with their information
  event_entries:
    templates:
      attempt: '<div class="title">{{theory_course_name}} attempt warning</div>{{count}} failed attempts in {{subject_category_name}}'
      expiry: '<div class="title">{{theory_course_name}} expiry warning</div> {% if past %}Expired{% else %}Expires in{% endif %} {{expires_in}} {% if past %} ago {% endif %}'
      sitting: '<div class="title">{{theory_course_name}} sitting warning</div> {{count}} sittings in use'
  exercises:
    exercise:
      edit: Edit
      delete: Delete
    create:
      edit: Edit
    flash_created: The exercise was created
    flash_destroyed: The exercise was deleted
    flash_cant_be_destroyed: You cannot delete this exercise because there is students attached
    form:
      cancel: Cancel
      submit: Save exercise
      create: Create exercise
      no_master_exercises_placeholder: Master exercise list is empty
    new:
      title: New exercise
  export_to_csv: Export to CSV
  flight:
    multi_engine: Multi engine
    pilot_flying: Pilot flying
    pilot_not_flying: Pilot monitoring
    single_engine: Single engine
    sim:
      single_engine: SE
      multi_engine: ME
  flights:
    flight:
      add_service_info: Add service info
      airborne: 'Airborne:'
      big_tacho_time: Timer is <b>more</b> than it should be.
      block: 'Block:'
      choose_plane: Choose aircraft
      contact_info: Reports and messages
      commercial_trip: Business
      correct_service_info: Service info
      day: Day
      expirations: Expirations
      flight: Flight
      flight_times: Flight time calculation
      landings: Landings
      multi_engine: Multi-engine
      night: Night
      no_val: 'No'
      remove_flight: remove flight
      right_landing_time: Taxi is out of the normal range
      right_tacho_time: Have you entered the correct Timer time?
      right_takeoff_time: Taxi is out of the normal range
      see_more: See more
      service_time: Service time
      single_engine: Single-engine
      small_tacho_time: Timer is <b>less</b> than it should be.
      tacho: 'Timer:'
      traning_trip: Training
      yes_val: 'Yes'
    flight_row:
      fly: Proceed
      disabled_text: The student must be on an active program, in order to enable registration
      disabled_text_own_program: Unable to do registrations on a program assigned to yourself
      edit_custom: Edit extra lesson
      credited: CREDITED
      inst_dot: INST.
    index:
      logbook: Logbook
    instructor_invoice_form:
      save: Save
    invoice_form:
      save: Save
    show_flight:
      airborne: 'Airborne:'
      airport: Airport
      approach: Approach
      arrival_airport: 'Arrival airport:'
      block: Block
      cross_country: 'Cross country:'
      departure_airport: 'Departure airport:'
      daytime: 'Daytime:'
      flight_rules: 'Flight rules:'
      flight_times: Flight times
      flight_type: 'Flight type:'
      full_stop: Full stop
      cc_true: "Yes"
      cc_false: "No"
      go_arounds: Go-around
      landing: 'Landing:'
      off_block: 'Off block:'
      on_block: 'On block:'
      pilot_flying: 'Pilot role:'
      plane: Aircraft
      tacho: Timer
      timer_finish: 'Timer finish:'
      timer_start: Timer start
      touch_and_go: Touch and go
  grades:
    remark: Comment
    title: Name
  groups:
    memberships:
      active_all: All active users
      active_administrators: Active administrators
      deactivated_administrators: Deactivated administrators
      flight_instructors: Active flight instructors
      deactivated_flight_instructors: Deactivated flight instructors
      ground_instructors: Active ground instructors
      deactivated_ground_instructors: Deactivated ground instructors
      active_instructors: Active instructors
      deactivated_instructors: Deactivated instructors
      active_renters: Active renters
      deactivated_renters: Deactivated renters
      active_crew: Active crew
      deactivated_crew: Deactivated crew
      active_staff: Active staff
      deactivated_staff: Deactivated staff
      active_guest: Active guest
      deactivated_guest: Deactivated guest
      active_students: Active students
      completed_students: Completed students
      standby_students: Standby students
      __students__without_program: Students without program
      discontinued_students: Discontinued students
      invalid: "This scope #{name} is not valid"
    new:
      title: Group
    edit:
      title: Group
    index:
      title: Groups
      active: Active
      deactivated: Deactivated
      name: Name
      permissions: Access
      members: Members
      no_active_groups: There are no active groups to be shown at the moment.
      no_deactivated_groups: There are no deactivated groups to be shown at the moment.
    show:
      title: Group
      name: Name
      members: Members
      group_type: System group
      no_members: There are no members in this group

    form:
      name: Name
      submit: Save
    group:
      user_count: '%{count} members'
      confirm_deactivate: "Are you sure, that you want to deactivate group: %{group_name}?"
    new_group: Create group
    active_groups: Active groups
    deactivated_groups: Deactivated groups
    no_active_groups: There are no active groups to be shown at the moment.
    no_deactivated_groups: There are no deactivated groups to be shown at the moment.
    deactivate: "Are you sure, that you want to deactivate group: %{group_name}?"
    edit_btn: Edit
    groups: Groups
    flash_created: The group has been created
    flash_updated: The group has been updated
    flash_merge: The groups has been merged
    flash_merge_error: You have to choose a group to be merged.
    report:
      date: Date
      dual: Dual
      invoice: Invoice
      invoice_marked: Invoice
      invoice_number: Invoice number
      mark_all: Mark all
      sim: SIM
      solo: Solo
  group_memberships:
    membership:
      header: Membership to %{group_name}
      category: Category
      name: Name
      roles: Roles
      user: User
      empty_membership: There are no members yet
      members: 'The following users are members of %{group_name}'
      type:
        group: Groups
        user_type: User types
        team: Classes
        program: Programs
        user: Users
      missing_resource: '%{watchable_type} resource has been deleted!'

    member:
      header: Membership to %{group_name}
      category: Category
      name: Name
      roles: Roles
      user: User
      empty_membership: There are no members yet
      members: 'The following users are members of %{group_name}'
      type:
        group: Groups
        user_type: User types
        team: Classes
        program: Programs
        user: Users
      missing_resource: '%{watchable_type} resource has been deleted!'
    index:
      description: |
        Memberships shows users belonging to this group. By selecting groups and/or individual users below you can assign new members to this group.
      done: Done
      name: Name
      share_btn: Share
      share_with: 'Share with:'
      membership_label: Member
      submit: Add
      title: '%{group_name} membership'
      title_guest: Guest user access
  kiosk_user:
    headers:
      booking: Bookings
      general: Aircraft
      certificate_center: Certificate warnings
      ground_school_warning: Ground training warnings
      maintenance_warning: Maintenance warnings
      departure_arrival: Departure/Arrival
    error:
      updated: The kiosk user could not be updated
      enabled: The kiosk user could not be enabled
      disabled: The kiosk user could not be disabled
    success:
      updated: The kiosk user is updated
      enabled: The kiosk user is enabled
      disabled: The kiosk user is disabled
    form:
      disable: Disable
      enable: Enable
      update: Disconnect
      students: Students
      usage: Pending kiosk connections
      instructors: Instructor and crew
      classrooms: Classroom
      aircraft: Aircraft
      disabled_url: "<enable in order to see the URL>"
      aircraft_status_overview: Aircraft status view
      aircraft_status_overview_explanation: This URL will show the aircraft status view
      departure_arrival_full: "Departure/Arrival view"
      departure_arrival_full_explanation: This URL will show both Departure/Arrival in one view
      departure_arrival_departure_only: Departure only view
      departure_arrival_departure_only_explanation: This URL will show only departures
      departure_arrival_arrival_only: Arrival only view
      departure_arrival_arrival_only_explanation: This URL will show only arrivals
      maintenance_overview: The maintenance view
      maintenance_overview_explanation: This URL will show the maintenance view
      complete_overview_title: Complete view
      complete_overview_explanation: This URL will show a complete list of all bookings for the current day.
      overview_title: "%{type} view"
      overview_explanation: "This URL will only show the %{type} in the booking view for the current day."
      filter_overview_title: "Filter: \"%{filter}\""
      filter_overview_explanation: "This URL will show information in the booking view according to the \"%{filter}\" filter for the current day."
      title: The kiosk solution can be used to display different areas of FlightLogger such as the booking page or aircraft status on various different monitors inside or outside your organization. You can use the kiosk solution in two different ways either 'Copy/paste URL' or 'Connect & approve' which are both described below.
      explanation: "<h4>Connect & approve</h4>
                    </p><p>With this option, first copy this URL <i>%{sub}.flightlogger.net/kiosk</i> and paste it into a browser on the monitor you want to display the kiosk screen on (you should not be logged in to FlightLogger on the monitor's browser).</br>
                    Then a 6 digit code will appear below allowing you to choose what area of FlightLogger you want to display on the kiosk screen.</p>"
      show_cancelled_bookings: Include cancellations
      include_flight_types: Include flight types
      successful_update: The kiosk user was updated!
      no_pending_connections: "There is no 6 digit code yet"
      url_explanation: "<h4>Copy/paste URL</h4></p><p>With this option, first choose in the dropdown the area of FlightLogger you want to display. Then copy the shown URL into a browser on the monitor you want to display your chosen area on.</p>"
      renew_explanation: "<h4>Disconnect</h4></p><p>Below you can disconnect all kiosk solutions on your account. <br> Please use this with caution as you will have to setup every screen again.</p>"
    kiosk_form:
      show_cancelled_bookings: "Show Cancellations"
      include_flight_types: "Include flight types"
  flight_time_limitations:
    index:
      title: Flight time limitations
      subtitle: Block time warning
      description: A color warning will appear after
      hours: hours
      color: color
      hours_within: block hours within
      trigger: will trigger
      warning: warning
      add: Add warning
      update: Update flight time limitations
    flash_updated: Flight time limitations updated
    flash_errors: Update failed with validation errors
    form:
      subtitle: Block time warning
      description: A color warning will appear after
      hours: hours
      color: color
      hours_within: block hours within
      trigger: will trigger
      warning: warning
      add: Add warning
  flight_time_warnings:
    icon_hint: Shows the user's flight and duty time limitation status
    warnings:
      title: Flight and duty time warnings
  ground_school_warnings:
    issues:
      sitting: "%{count} sittings in use"
      expiry:
        past: "%{count} days since expiry"
        future: "%{count} days to expiry"
      attempt: "%{count} failed attempts"
    warnings:
      title: Ground training warnings
    reply_title:
      attempt: 'Regarding {{theory_course_name}} attempt warning - {{count}} failed attempts in {{subject_category_name}}'
      expiry: 'Regarding {{theory_course_name}} expiry warning - {% if past %}Expired{% else %}Expires in{% endif %} {{expires_in}} {% if past %} ago {% endif %}'
      sitting: 'Regarding {{theory_course_name}} sitting warning - {{count}} sittings in use'
  helpers:
    :true: 'Yes'
    :false: 'No'
    booking_helper:
      programs: Programs
      students: Students
      classes: Classes
    reports:
      rentals: Rentals
    users:
      teams: Classes
  hold_items:
    remember_hil: "<b>Attention:</b> This student has unfinished exercises click the eye to reveal and complete these"
    title: Hold Item List
    toggle_table: See hold item list
  invoice_trips:
    invoice_form:
      save: Save
  landings:
    invoice_form:
      save: Save
    landing:
      create_new_airport: Create new airport
      in: in
      approaches: Approach
      go_arounds: Go-around
      landings: Full stop
      touch_and_gos: Touch and go
  landings_helper:
    approaches: Approach
    go_arounds: Go-around
    landings: Full stop
    touch_and_gos: Touch and go
  duty_time_limitation_types:
    edit:
      update_duty_time_type: Update duty time requirement
    new:
      create_duty_time_type: Create duty time requirement
    duty_time_limitation_type:
      deactivate: "Are you sure, that you want to disable the limitation: %{duty_time_limitation_type_name}?"
    index:
      create: Create requirement
      delete: Delete
      delete_confirm: Are you sure, that you want to delete the requirement '%{name}'
      type: Name
      title: Duty time requirements
      limitation: Limitation
      update: Update
      used_by: Used by
      active: Active
      deactivated: Deactivated
      no_active_duty_time_limitation_types: There are no active duty time requirements to be shown at the moment
      no_deactivated_duty_time_limitation_types: There are no deactivated duty time requirements to be shown at the moment
    form:
      new: Create requirement
      edit: Update requirement
      defaults_description: 'A duty time limitation warning will appear after:'
      include: 'Include dutys from:'
      expired_description: Expired
      warnings_description: Warnings
      hours_within: duty hours within
      limitation: 'Limitation: '
      duty_time_limitation_requirements_title: Additional warnings
      duty_time_limitation_requirements_no_types: There are no duty time limitation requirements to choose from
      triggers: triggers
      expired: expired
      warning: warning
      color: color
  master_requirement_lists:
    index:
      ftl_title: Flight time
      duty_title: Duty time
      maintenance_title: Maintenance
      certificate_title: Certificates
      duty_time_title: Duty time
  filter_lists:
    index:
      booking_title: Booking pages
      certificate_title: Certificate warnings
      ftl_title: Flight time limitation warnings
      ground_school_title: Ground training warnings
      maintenance_title: Maintenance warnings
  layouts:
    administration_menu:
      activities: Operations
      certificate_warning_filters: Filters - Certificate warning
      changelogs: Changelogs
      maintenance_warning_filters: Filters - Maintenance warning
      ground_school_warning_filters: Filters - Ground school warning
      flight_time_warning_filters: Filters - Flight time limitation warning
      booking_filters: Filters - Booking
      administration: Administration
      airports: Airports
      classrooms: Classrooms
      cancellation_reasons: Cancellation reasons
      customers: Customers
      order_type: Transaction types
      planes: Aircraft
      teams: Classes
      programs: Programs
      requirements: Requirements
      requirement_types: Requirements
      theory_courses: Theory courses
      certificate_type: Master certificate list
      maintenance_type: Master maintenance list
      master_requirement_lists: Master requirement lists
      filter_lists: Filters
      account_settings: Account settings
      flight_time_limitation: Master flight time limitation list
      quickbooks: "QuickBooks invoices"
      collaborations: "Collaborations"
      groups: Groups
      roles: User permissions
    application:
      dep_arr: Dep/Arr
      activities: Activities
      operations: Operations
      documents: Documents
      flightlogger: FlightLogger
      my_program: My program
      rental: Rental
      sms: SMS
      sms_twipsy: Safety Management System
      theory: Theory
    application_responsive:
      activities: Operations
      documents: Documents
      flightlogger: FlightLogger
      my_program: My program
      rental: Rental
      sms: SMS
      sms_twipsy: Safety Management System
      theory: Theory
    booking_menu:
      approve_a_plane: Approve aircraft booking
      approve_a_sim: Approve simulator booking
      book: Create bookings
      bookings: Bookings
      monthly_overview: Monthly view
      my_bookings: Daily/Weekly view
      overview: Day/week/month view
      changelog_booking_view: List view/changelogs
      rent_a_plane: Request aircraft booking
      rent_a_sim: Request simulator booking
      weekly_overview: Weekly view
    kiosk:
      flightlogger: FlightLogger
      logout: Logout
    report_menu:
      fuel_report: Fuel report
      analytic_dashboard: FlightLogger Business Insights
      airports_report: Airport reports
      accounting_balance: Accounting balances
      accounting_transactions: Accounting transactions
      accounting_sales: Accounting sales
      booking_report: Booking statistics
      cancelled_booking_report: Cancellation report
      certificates_report: Certificates report
      invoicing_report: Invoice report
      monthly_report: Monthly report
      planes_report: Aircraft reports
      production_report: Production report
      program_changelog: Program changelog
      flight_report: Raw data - flight report
      theory_report: Raw data - theory report
      duty_report: Raw data - duty report
      reports: Reports
    user_menu:
      calendar_sync: Sync with calendar
      logbook: Logbook
      logout: Log out
      my_alarms: My alarms
      my_profile: Profile
      start_duty_time: Start duty time
      stop_duty_time: Stop duty time
      update_info: Edit info
      update_password: Edit password
      update_picture: Edit image
      desktop_layout: Full website
      mobile_layout: Mobile website
      help: HELP
      help_center: Help center
      support: Get support
      training: Refresher training
      updates: Software updates
      refer_and_save: Refer and save
    users_menu:
      administrators: Administrators
      instructors: Instructors
      renters: Renters
      students: Students
      crew: Crew
      staff: Staff
      guests: Guests
      users: Users
      search: Search
    email_menu:
      email: Email
      email_cmm: Email CMM
    warnings_menu:
      warnings: Warnings
      certificate_warnings: Certificate warnings
      ground_school_warnings: Ground training warnings
      maintenance_warnings: "Maintenance warnings"
      flight_time_warnings: Flight and duty time warnings
      squawk_warnings: Discrepancy warnings
    help_center_menu:
      help_center: Help center
      support: Get support
      training: Refresher training
      updates: Software updates
      refer_and_save: Refer and save
    help_center_phone_menu:
      help_center: Help center
      support: Get support
      training: Refresher training
      updates: Software updates
      refer_and_save: Refer and save
    job_progress:
      job_progress_modal:
        cancel: Cancel
      job_progress_bar:
        completed: completed
    member_navbar:
      desktop_sidebar:
        index:
          profile_page: "My Profile"
          link_page: "Merge Accounts"
          landing_page: "my|FlightLogger"
          logbook_page: "My Logbook"
          program_page: "My Programs"
          token: "Token"
          api_keys: "API Keys"
        footer:
          logout: Log Out
          settings: Settings
      mobile_navbar:
        index:
          profile_page: "My Profile"
          merge_page: "Merge Accounts"
          landing_page: "My Organizations"
          logbook_page: "My Logbook"
          program_page: "My Programs"
          logout: "Log Out"
          settings: Settings
          api_keys_page: "API Keys"
          my_flightlogger: "my|FlightLogger"
    admin:
      organizations: "Organizations"
    user:
      user_show_header:
        show_emergency_contact: Emergency contact
        show_normal_contact: Basic info
        show_references: References
        show_notes: Notes
        show_user_activity: User activity
        user_blocked: The user is blocked
  user_duty_times:
    update:
      success: Duty time successfully created
      error: Failed to create duty time
    stop_duty_appearance:
      cancel_duty_time: Cancel duty
      create: Submit
      duty_start_time: Start time
      duty_end_time: End time
      title: Create duty time
  lectures:
    edit:
      title: Update lesson
    flash_cannot_be_cloned: Unable to clone lesson
    flash_cant_destroy: The lesson couldn't be deleted, because some students uses
      it.
    flash_cannot_be_found: The lesson couldn't be found
    flash_cloned: Lesson cloned
    flash_created: The lesson has been created. Add a Category which can contain exercises.
    flash_destroyed: The lesson has been deleted
    form:
      headline_pre_flight: 'Briefing/Debriefing'
      headline_syllabus: 'Syllabus times'
      headline_additional: 'Additional times'
      headline_vrflow: 'VRflow'
      pre_flight: 'Briefing'
      post_flight: 'Debriefing'
      submit: Save lesson
    new:
      title: New lesson
    show:
      add_another_lecture_to_this_program: Create another lesson in this program
      add_category: Create category
      add_exercise: "Create exercise in '%{name}'"
      delete: Delete
      edit: Edit
      go_back_to_program_revision: Return to program revision
  lectures_mailer:
    lecture_info:
      heading: "Some basic information about the lesson:"
      instructor: "Instructor:"
      lesson: "Lesson:"
      program: "Program:"
      student: "Student:"
      date: "Date:"
  lib:
    roles_with_validator:
      unspecified: Users added with roles not specified
      duplicates: "%{user} is added more than once"
      amount: The number of %{role} must be between %{min} and %{max}
      amount_infinity: The number of %{role} must be equal or greater than %{min}
    chrono_validator:
      later_than: must be later than %{other}
    booking_validator:
      already_booked: "%{bookee} is already booked in this time slot"
      recurrences_too_far_ahead: booking can consist of a maximum of 365 occurrences
      chrono: "start time has to be before the end time"
      chrono_briefing: "has to be before debriefing"
      chrono_start: "has to be before start"
      chrono_end: "has to be before end"
      chrono_debriefing: "has to be before debriefing"
      chrono_start_end: "has to be before end"
      shift: "%{bookee} is not set to be available in this time slot"
      blocked_by_warning: You can not proceed to the registration due to unresolved warnings
      outside_visibility_window: You cannot create a booking outside your visibility window
      cannot_rent: You cannot rent this plane
    user_lecture_validator:
      registration_blocked_by_warning: You can not submit the registration due to unresolved warnings
      proceed_blocked_by_warning: You can not proceed to the registration due to unresolved warnings
    user_warnings_validator:
      blocked_by_warning: "%{user} has unresolved warnings"
    team_theory_warnings_validator:
      blocked_by_warning: "%{user} has unresolved warnings"
    flight_validator:
      activity_required: required
      flight_type_required: required
      has_to_be_after: has to be after %{attribute}
      before_or_equal: "%{other} has to be same as or before %{primary}"
      after_or_equal: "%{other} has to be same as or after %{primary}"
      be_within: "%{other} has to be within or same as %{primary}"

      has_to_be_after_or_equal: has to be after or equal %{attribute}
      sum_has_to_be: sum of %{field1} and %{field2} has to be %{expected} was %{actual}
      registration_blocked_by_warning: You can not submit the registration due to unresolved warnings
      should_round_to_minutes: "should be valid minutes, was %{actual} seconds"
      should_be_whole_minute: "should be whole minute, had remainder of %{seconds} seconds"
      rental_booking_required: "The chosen booking has already been completed and this registration can thus not also be completed. Leave this page open and delete the other rental in order to save this rental"
      trip_booking_required: "The chosen booking has already been completed and this registration can thus not also be completed. Leave this page open and delete the other operation in order to save this operation"
      user_lecture_booking_required: "The chosen booking has already been completed and this registration can thus not also be completed. Leave this page open and reset the other lesson in order to save this lesson"
    user_role_validator:
      needs_to_be_student: The user must be a student when there is an associated program.
      verify_role: You haven't specified a role for the user.
      cant_be_kiosk_mode: The user cant be kiosk mode when also having other roles.
    trip_validator:
      blank: must be specified
    payment_validator:
      both_needs_to_change: Status and status changed at must be changed together.
  logbooks:
    show:
      manual_entries: External
      flight_logger_entries: Internal
      logbook: Logbook
  maintenance:
    to_approval:
      no_maintenances: There are no maintenance parts requiring approval.
    current:
      no_maintenances: There are no current maintenance parts.
    previous:
      no_maintenances: There are no previous maintenance parts.
    shared:
      serial: 'Serial #'
      expiry: Expiry
      to_approval: Requiring approval
      expired: Expired
      approved: Approved
      missing: Missing
      by: by
      uploaded: Uploaded
      confirm_reject: Are you sure you want to reject the maintenance?
      renew: Renew
      current_maintenances: Current maintenance parts
      previous_maintenances: Previous maintenance parts
      creation_date: Creation date
      reject: Reject
      edit: Edit
      name: Name
      reason: Reason
      unapproved_and_expired_maintenances: Rejected or expired maintenances
      no_maintenances: There are no maintenances yet
    level_must_meet_requirement: "Level must meet the maintenance requirement specification"
  renew_maintenance_parts:
    show:
      title: Renew maintenance part
  maintenance_parts:
    new:
      title: Create maintenance part
    edit:
      title: Update maintenance part
    flash_created: The maintenance part is created
    flash_updated: The maintenance part is updated
    approved: Part approved
    rejected: Part rejected
    serial_number_in_use: already in use by %{description} on plane %{plane}
    serial_number_missing: can't be blank
    fields:
      service_at: Next service @
      maintenance_type: Maintenance
      expiration_date: Date
      expiration_airborne_time: Airborne time
      expiration_tach_time: Tach time
      expiration_airswitch_time: Airswitch time
      expiration_VDO_time: VDO time
      expiration_hobbs_time: Hobbs time
      expiration_heater_hobbs_time: Heater Hobbs time
      expiration_flight_time_time: Flight time
      expiration_EDU_time: EDU time
      expiration_DATCON_time: DATCON time
      expiration_block_time: Block time
      expiration_cycles: Cycles
      serial_number: 'Serial #'
      upload_certificate: Upload certificate
  maintenance_presenter:
    renewed: Renewed
    rejected: Rejected
    expiration_reached: Expired
  maintenances:
    maintenance_approved: maintenance approved
    maintenance_unapproved: maintenance rejected
    edit:
      title: Edit maintenance
    flash_created: The maintenance is created
    flash_updated: The maintenance is updated
    fields:
      no_file: No file chosen
      choose_file: Upload maintenance
    form:
      confirm_reject: Are you sure you want to reject the maintenance?
      reject: Reject maintenance
      submit: Save maintenance
    new:
      title: Create maintenance
  master_lists:
    flash_destroyed: The master list has been deleted
    flash_created: The master list has been created
    no_exercises: Master exercise list is empty, please create master exercises before continuing
    index:
      title: Exercise master list for %{program}
      title_master_list_group: Statistic groups
      confirm_delete: Are you sure you want to delete master list and revert back to similarity statistic?
      confirm_disable: "Are you sure, that you want to deactivate the aircraft: %{plane_name}?"
  master_list_groups:
    show_exercises:
      title_master_list_exercises: Master Exercises (%{group})
  master_list_exercises:
    table_row:
      confirm_disable: "Are you sure, that you want to deactivate the master list exercise?"
  messages:
    flash_created: The wall post is created
    flash_updated: The wall post has been updated
    flash_pinned: The wall post has been pinned
    flash_unpinned: The wall post has been unpinned
    flash_destroyed: The wall post has been deleted
    flash_error: The wall post doesn't exists.
    form:
      submit: Save wall post
      labels:
        message: Message
    new:
      title: Add wall post
    edit:
      title: Edit wall post
  models:
    alarm:
      expires: 'Expiry date: %{date}'
      insurance: Insurance
      medical: Medical
      missing: Missing
      radio: Radio
      unapproved: Updated
      unapproved_and_expires: 'Updated with expiry date: %{date}'
    booking:
      activity: Operation
      approved: Approved
      classroom: Classroom
      crew_lecture: Multi student
      lecture: Single student
      maintenance: Maintenance
      meeting: Meeting
      not_approved: Awaiting approval
      plane: Aircraft
      rental: Rental
      student: Student
      students: Students
      team: Class
      theory: Extra theory
      theory_multiple: Class theory
      type: Type
      progress_test: "Progress test"
      theory_release: "Theory release"
      exam: "Exam"
      theory_exam: "Exam"
      type_questionnaire: "Type questionnaire"
      cannot_delete_completed_or_cancelled: "Cannot delete completed or cancelled bookings"
      button:
        decline_student_request: "Decline request"
        approve_student_request: "Approve request"
    canceled_lecture:
      aircraft: Aircraft
      instructor: Instructor
      other: Other
      school: School
      student: Student
      weather: Weather
    cancellation_reason:
      types:
        school_flight: School flight
        operation: Operation
        rental: Rental
        class_theory: Class theory
        extra_theory: Extra theory
        progress_test: Progress test
        theory_release: Theory release
        theory_exam: Exam
        type_questionnaire: Type questionnaire
    certificate:
      approved: Approved
      expired: Expired
      migrated: Migrated
      not_required: Not required
      to_approval: Requiring approval
      unapproved: Rejected
    flight:
      must_have_landings: Must have at leat one landing
    flight_hour:
      previously: Previously
    sitting:
      number_warning: cannot be greater than 6
    special_revision:
      name: Custom lesson
      name_with_title: Custom lesson - %{title}
      repetition: Repetition
      extra_lesson_name: Extra lesson
      extra_lesson_name_with_title: Extra lesson - %{title}
    theory_exam:
      four_tries: More than 4 attempts have been performed in this subject
      must_have_sitting: must be specified
    users:
      blocked_reason: You do not have access to this FlightLogger account anymore. Please contact the administrators of this account to gain access again.
      failed_login_attempt_reason: You have had too many login attempts recently. Please wait a while and try again later.
      inactive_reason: You have been inactive for too long. Please contact your school
        for more information
      needs_to_be_student: The user must be a student when there is an associated
        program.
      verify_role: You haven't specified a role for the user.
    users_team:
      is_on_team: is already in this class
    rental:
      title: "Rental %{date}"
    sitting_group:
      name: "%{position} Sitting group"
  top_up_items:
    default_payment_label: User balance top-up
  order_items:
    correction_with_ref: "Correction to %{transaction_id} (%{comment})"
    flight_created: "Flight created"
    flight_changed_from: "Flight changed from"
    flight_changed_to: "Flight changed to"
    flight_destroyed: "Flight deleted"
    aircraft_changed_from: "Aircraft changed from"
    aircraft_changed_to: "Aircraft changed to"
    user_lecture_completed: "Lesson completed"
    user_lecture_changed: "Lesson changed"
    user_lecture_deleted: "Lesson deleted"
    user_lecture_reset: "Lesson deleted"
    user_lecture_order_name: "Instructor briefing/%{lecture_name}"
    create_fail_label: The transaction could not be created, please try again
    create_label: The transaction was created
  order_types:
    create: Create Transaction type
    description: Description
    empty_orders: There are no transactions yet
    total: Total
    flash_update_failed: The transaction type could not be updated, please try again
    free_text: Freetext
    price_label: Price
    update: Update Transaction type
    edit:
      title: Edit Transaction type
    index:
      create_btn: Create Transaction type
      title: Transaction types
      confirm_delete_order_type: "Are you sure, that you want to delete the transaction type: '%{order_type_description}'?"
    new:
      title: New transaction type
    price: Price
    type: Description
    update_fail_label: The transaction type could not be updated, please try again
    update_label: Transaction type was updated
  confirm_emails:
    flash_error: We're sorry, but your confirm link is invalid or expired. Please try again
    flash_success: Your FlightLogger email %{email} has been confirmed.
    modal:
      mail_sent: Mail sent, check your inbox.
      header: Please confirm your email address
      body: Before activating 2FA we need to ensure that your email is properly configured.
      confirmation_send_to: FlightLogger has sent a confirmation email to %{email}
      resend: Resend
    new:
      mail_sent: Mail sent, check your inbox.
      header: Please confirm your email address
      confirmation_send_to: FlightLogger have send a confirmation email to %{email}
      resend: Resend
    pending_request:
      awaits_confirmation: Your email change is pending confirmation. Please click the confirmation link in the email we sent to %{email}.
      resend: Resend confirmation email
      resend_processing: Resending...
      cancel: Cancel email change
      cancel_processing: Cancelling...
  password_resets:
    edit:
      save_password: Update
      title: Update Password
      sub_title: Enter a new password and confirm it below
      password: Password
      confirm_password: Confirm Password
      new_password: Update Password to Enter my|FlightLogger
      create_password: Update
      pitch_header: 'With my|FlightLogger You Get:'
      pitch_access: Single sign-on to all your FlightLogger organizations
      pitch_logbook: Electronic logbook gathering all flights in one system
      organizations: 'Your Current FlightLogger Organizations:'
      organizations_alt: 'Your Current FlightLogger Organizations:'
    flash_created: An email has been sent to you with instructions if the email exists
    flash_error: We're sorry, but we could not locate your account
    flash_updated: The password has been updated, you can now log in
    account_does_not_exist: This account does not exist
    new:
      submit: Send
      title: Forgot Password
      sub_title: Enter your email to receive new login instructions
  welcomes:
    create_password: Create Password
    password: Password
    confirm_password: Confirm Password
    create: Create
    sign_in: Sign In
    email: Email
    submit: Sign In
    invalid_password: Invalid
    forgot_password: Forgot Password?
  passwords:
    edit:
      save_password: Update
      title: Please Update Your Password
    flash_updated: Your password has successfully been updated!
  payment_methods:
    create:
      success: You have added a new card
      error: Unable to add card
    new:
      add_payment_method: Add a credit or debit card
    form:
      add_card: Add card
      card_number: Card number
      exp_date: Exp. date
      security_code: Security Code
      label: Label
      currency: Currency
      email: Email
      first_name: First name
      last_name: Last name
      phone: Phone
      address: Address
      post_code: Post code
      city: City
      country: Country
      state: State
      card_details: Card details
      accept_major_ccs: FlightLogger accepts major credit and debit cards
      enable_automatic_payment: Enable automatic payment
      automatic_payment_agreement: By enabling automatic payment, you agree that %{account_name} will automatically charge payment requests to your card until you disable. You may disable at any time to avoid future automatic charges.
      after_save: After clicking “Add card” you may be redirected to your bank to validate the payment method.
    set_automatic_confirm:
      enable_automatic_payment: Enable automatic payment
  payments:
    callback_success:
      success: Successfully processed payment
    callback_cancel:
      cancel: Payment has been cancelled
      internal_error: Failed to complete payment, please try again later or contact support
      payment_exist: Payment already exist in QuickBooks
    receive_payment_initiation:
      failed: Receive payment initiation through FL Pay failed
      success: Successfully processed payment
    please_wait:
      title: Please Wait...
      checkout_description: Redirecting to checkout form
      preferred_payment_description: Payment is processing with your automatic payment method
    refund_payment:
      title: 'Refund'
    send_reminder_form:
      email_label: "Email"
      send_button_title: "Send"
      title: "Send reminder by email"
    cancel:
      cancel_success: "Successfully cancelled"
      cancel_error: "There was a problem cancelling the payment"
    show:
      modal_header: "Pay %{label}"
      pay_with_card: Pay
      pay_with_bluesnap_checkout: Pay
    download_receipt_pdf:
      failed: "Failed to download receipt"
    pay_checkout:
      redirecting_to_checkout: Redirecting to checkout
    pay_saved_card:
      payment_processing: Payment processing
    send_reminder:
      invalid_email: "Email %{email} is invalid"
      send_email: "Email sent to %{email}"
    refund:
      refund: "Are you sure you want to refund %{amount} to %{name}?"
  price_lists:
    flash_created: Price list created
    flash_cloned: Price list cloned
    flash_clone_failed: Failed to clone the price list
    index:
      title: Price lists
      active: Active
      deactivated: Deactivated
      price_list: Price list
      rental_price: Rental price
      programs: Programs
      none_active: There are no active price lists to be shown at the moment
      none_deactivated: There are no deactivated price lists to be shown at the moment
      go_back: Return to price lists
      create: Create price list
  price_list_items:
    flash_created: Price list item created
    flash_updated: Price list item updated
    flash_destroyed: Price list item deleted
    flash_destroy_failed: Failed to delete the price list item
    index:
      title: "Price list: %{price_list_name}"
      create: Create aircraft price
      update_label: Aircraft program price was updated
      added_price_to_aircraft: Aircraft price list applied
      pricing: Pricing
      aircraft: Aircraft
      program: Program
      hourly_rate: Hourly rate
      flight_log: Flight log
      no_prices: There are no prices yet
      call_sign: Call sign
      aircraft_model: Aircraft model
      applied_to_aircraft: Applied to aircraft
      applied_by: Applied by
      create_label: Aircraft price was created
      destroy_label: Aircraft price was deleted
      advanced_pricing_enabled: Advanced pricing enabled
      advanced_pricing: Advanced pricing
      none_pricing: There are no program prices to be shown at the moment
      none_aircraft: There are no aircraft prices to be shown at the moment
      nope: No
    form_advance_pricing:
      flight_log: Flight log
      vfr: VFR hourly rate
      dual: DUAL / SIM
      solo: SOLO
      spic: SPIC
      ifr: IFR hourly rate

    price_list_item_prices:
      confirm_delete_price: Are you sure, that you want to delete the price?
    new:
      title: "New aircraft price on: %{price_list_name}"
      is_it_rental: Rental price
    edit:
      title: "Update aircraft price on: %{price_list_name}"
      is_it_rental: Rental price
    price_list_item_planes:
      call_sign: Call sign
      aircraft_model: Aircraft model
      applied_to_aircraft: Applied to aircraft
      applied_by: Applied
      no_prices: No prices
      apply_price_list: Apply price list
      nope: No
      confirm_changes: "Are you sure you want to apply %{price_list_name} to:"
      overwrite_warning: "If prices already exist for the prices on %{price_list_name} they will be overwritten"
      cancel: Cancel
      apply: Apply
    form:
      aircraft_price: aircraft price
  planes:
    show:
      booking: Booking
      bookings: Bookings
      cancelled_bookings: Cancelled bookings
      availabilities: Availabilities
      documents: Documents
      tracking: Flight tracking
      edit_plane: Edit aircraft
      maintenance: Maintenance
      squawks: Discrepancies
      reports: Reports
      settings: Settings
      update_image: Update Image
      maintenance_requirements: Requirements
      edit_flight_logs: Edit Flight Logs
    maintenance_overview:
      service_date: Service date
      service_type: Service type
      show_alarms: SHOW ALARMS
      show_all: SHOW ALL
      tacho_times: Maintenance overview
      time_left: Time left
      call_sign: Call sign
      hours: " hours"
      maintenance_empty: No aircraft require maintenance. Keep up the good work!
    service_info:
      correct_service_info: Service info
      service_type_hint: For example 100 Hours
    form:
      general: "General"
      current: "Current"
      enabled_pmf: Enable PM/PF
      enabled_pmf_default: Default PM/PF value
      measurement: '- measurement'
      timer_warning_percent: '- Timer warning'
      display_landings: 'Show in A/C status'
      fuel_coefficient: Fuel coefficient
      type_of_timer: Type of timer
      differ_block_timer: If block differ
      differ_hobbs: If hobbs differ
      differ_heater_hobbs: If heater hobbs differ
      differ_tach: If tach differ
      differ_airswitch: If airswitch differ
      differ_VDO: if VDO differ
      differ_flight_time: if flight time differ
      differ_DATCON: if DATCON differ
      differ_EDU: if EDU differ
      from: from
      airswitch: Airswitch
      hobbs: Hobbs
      heater_hobbs: Heater Hobbs
      tach: Tach
      VDO: VDO
      flight_time: Flight time
      DATCON: DATCON
      EDU: EDU
      timer_disabled_info: "There is currently a price set on this aircraft which charges according to this log. Delete this price via Administration->Aircraft in order to delete"
      call_sign_info: "Call sign.\nUsed to represent the aircraft in FlightLogger (bookings, registrations, reports, etc.)\nNote that the 'Flight tracker' field (used for live flight tracking on the Dep/Arr and Booking pages) is automatically updated when changing the call sign but can be manually edited afterwards."
      flight_tracker_info: "Flight tracker.\nUsed for live flight tracking on the Dep/Arr and Booking pages through your preferred flight tracking provider.\nNote that the 'Flight tracker' field (used for live flight tracking on the Dep/Arr and Booking pages) is automatically updated when changing the call sign but can be manually edited afterwards."
      select_aircraft_model: Select Aircraft Model
      timer: Timer
      type: Type
      current_title: Shows current and last flight landings on Aircraft status
      current_fuel: Fuel
      current_airport: Airport
    index:
      active: Active
      deactivated: Deactivated
      call_sign: Call sign
      registration: Registration No.
      new_plane: New aircraft
      old_planes: Deactivated aircraft
      plane_type: Aircraft model
      pricing: Pricing
      price_lists: Price lists
      rental_enabled: Available for rental
      title: Aircraft
      no_active_planes: There are no active aircraft to be shown at the moment
      no_deactivated_planes: There are no deactivated aircraft to be shown at the moment
      go_back_to_aircraft: "Return to aircraft"
      create_aircraft: Create aircraft
      aircraft_models: Aircraft models
    reports:
      aircraft_report: Aircraft Report
    report:
      title: Aircraft
      call_sign: Call sign
      plane_type: Aircraft model
    old:
      active_planes: Active aircraft
      call_sign: Call sign
      plane_type: Aircraft model
      pricing: Pricing
      title: Deactivated aircraft
    plane:
      confirm_disable: "Are you sure, that you want to deactivate the aircraft: %{plane_name}?"
      prices: Prices
    flash_activated: "The aircraft has been activated"
    flash_deactivated: "The aircraft has been deactivated"
    calendar:
      calendar_synchronization: Sync with calendar
      calendar_explaination: |
        Using the link below you can synchronize bookings of this aircraft to your calendar.

        You can revoke access by clicking renew which will generate a new link for calendar synchronization. The previous link will cease to work when a new link is generated.
      renew: Renew
      public_link: Feed with minimal information (suiteable for public sharing)
      private_link: Feed with all booking information including comment and document links
      renew_explaination: Renew calendar link
      guides_info: The video guides below will guide you through the set up of calendar synchronization with different calendars. You can ignore the first few seconds as they refer to synchronization of your personal calendar feed.
    form_current:
      current: "Current"
      display_on_status: 'Show in A/C status'
      landings: "Landings"
  aircraft_models:
    show:
      booking: Booking
    index:
      active: Active
      deactivated: Deactivated
      name: Model
      aircraft_type: Type
      aircraft_model: Class
      title: Aircraft Models
      no_active_models: There are no active aircraft models to be shown at the moment
      no_deactivated_models: There are no deactivated aircraft models to be shown at the moment
      go_back_to_aircraft: "Return to aircraft"
      create_aircraft_model: Create aircraft model
      aircraft_models: Aircraft models
    edit:
      delete_image: Delete current image
    remove_image:
      success: The aircraft model image has been successfully removed
    reports:
      aircraft_report: Aircraft Report
    report:
      title: Aircraft
      call_sign: Call sign
      plane_type: Aircraft model
    old:
      active_planes: Active aircraft
      call_sign: Call sign
      plane_type: Aircraft model
      pricing: Pricing
      title: Deactivated aircraft
    plane:
      confirm_disable: "Are you sure, that you want to deactivate the aircraft: %{plane_name}?"
      prices: Prices
    flash_activated: "The aircraft has been activated"
    flash_deactivated: "The aircraft has been deactivated"
    calendar:
      calendar_synchronization: Sync with calendar
      calendar_explaination: |
        Using the link below you can synchronize bookings of this aircraft to your calendar.

        You can revoke access by clicking renew which will generate a new link for calendar synchronization. The previous link will cease to work when a new link is generated.
      renew: Renew
      public_link: Feed with minimal information (suiteable for public sharing)
      private_link: Feed with all booking information including comment and document links
      renew_explaination: Renew calendar link
      guides_info: The video guides below will guide you through the set up of calendar synchronization with different calendars. You can ignore the first few seconds as they refer to synchronization of your personal calendar feed.
    form_current:
      current: "Current"
      display_on_status: 'Show in A/C status'
      landings: "Landings"
    form:
      class_disabled: "Editing blocked — this model is in use by existing aircraft."
    aircraft_model:
      aircraft_type:
        airplane: Airplane
        helicopter: Helicopter
      aircraft_class:
        single_engine: Single-engine
        multi_engine: Multi-engine
        simulator: Simulator
  plane_documents:
    body:
      confirm: Are you sure you want to delete the document
      delete: Delete
      name: Name
      new_document: Upload documents
      new_folder: New folder
      update: Edit
      modified: Modified
      no_documents: There are no documents yet
    breadcrumbs:
      documents: Documents
    edit:
      title: Edit document
    flash_created: The document has been uploaded
    flash_destroyed: The document has been deleted
    flash_updated: The document has been updated
    index:
      confirm: "Are you sure you want to delete this document?\nThis can not be undone!"
      delete: Delete
      documents: Documents
      name: Name
      new_document: Upload new document
      update: Edit
      uploaded_at: Uploaded at
      no_documents: There are no documents yet
    new:
      title: Upload new document
    folders:
      confirm: Are you sure you want to delete the folder
    documents:
      confirm: "Are you sure you want to delete this document?\nThis can not be undone!"
      no_documents: There are no documents yet
  plane_document_folders:
    edit:
      title: Edit folder
      no_file: No file chosen
      choose_file: Choose file
    flash_cant_destroy: The folder can't be deleted, because it contains files.
    flash_created: The folder is created
    flash_destroyed: The folder is deleted
    flash_updated: The folder is updated
    new:
      title: Create folder
      no_file: No file chosen
      choose_file: Choose file
    show:
      confirm_delete: Are you sure you want to delete this folder?
      delete: Delete
      documents_in: 'Documents in '
      name: Name
      members: Members
      update: Edit
  program_revisions:
    program_revision:
      update: Edit
    form:
      submit: Save revision
      settings: "Settings"
      flight_training: "Flight Training"
      ground_training: "Ground Training"
      comment: "Note"
      progressbar_label: Show progressbar
      show_remaining_time: Show remaining time
      progressbar: "Progress bar"
      contains: "Contains"
      has_flight_training: "Flight Training"
      has_theory_training: "Ground Training"
      contains_hint_students: Cannot enable or disable as long as students are on this revision
      contains_hint_active_rev: Cannot enable or disable as long as this revision is selected as the active one
      contains_hint_lessons: Cannot disable as long as any lessons are attached to this revision
      cbta_hint_students: You cannot enable or disable CBTA as long as students are on this revision
      cbta_hint_lessons: You cannot disable CBTA as long as any lessons are attached to this revision
      cbta_hint_active_rev: You cannot enable or disable CBTA as long as this revision is selected as the active one
      count_me_time: "Count ME Sim as Multi Engine (ME)"
    form_flight:
      flight_time_columns: 'Customise program overview:'
      country_specific_columns: 'Country specific settings:'
      cbta_hint_active_rev: You cannot enable or disable CBTA as long as this revision is selected as the active one
      cbta_hint_lessons: You cannot disable CBTA as long as any lessons are attached to this revision
      revision_default_title: "Default settings:"
      lesson_landing_default: Default to 1 full stop for all lessons with no syllabus time
      has_theory_training: Has theory training
      has_flight_training: Has flight training
      activate_ptr: Use additional Pilot Training Record gradings (Canada)
      cbta_from_non_cbta: "You are about to create a revision with CBTA activated.\n\nBefore this revision can be taken in use all exercises have to be moved into their associated Flight Phases. Lesson names, briefing and flight times will be copied automatically.\n\nGo to each lesson in the new revision and move all exercises into their associated Flight Phases to make this revision usable."
      change_to_cbta_msg: "You are about to activate CBTA on this revision.\n\nBefore this revision can be taken in use all exercises have to be moved into their associated Flight Phases.\n\nOnce activated you cannot deactivate CBTA on this revision again if any lessons exists on it!"
    edit:
      title: Edit revision
      error: Could not edit revision. Please try again.
    new:
      title: Create new revision
    show_flight:
      add_new_lecture_to_phase: Create lesson in '%{name}'
      header_cbta: CBTA
      header_cbta_title: Lessons that are CBTA compliant
      confirm_delete: Are you sure, that you want to delete %{name}?
      delete: Delete
      cannot_add_lesson: "You cannot add lessons as long as students are on this revision"
    show:
      add_new_phase: Create phase
      clone: Clone
      confirm_clone: Are you sure, that you want to clone %{name}?
      confirm_delete: Are you sure, that you want to delete %{name}?
      delete: Delete
      description: Description
      edit: Edit
      go_back_to_program: Return to program
      name: Name
      cloning_in_progress: "Cloning in progress - "
      cbta_not_coverted_title: Lesson not fully converted
      cbta_without_norm_title: Has exercises without norm
      cbta_check_title: Lesson is CBTA comliant

      program_name: "%{program_name} (%{revision_name})"
      ground: "Ground"
      settings: "Settings"
      flight_training: "Flight Training"
      ground_training: "Ground Training"
    time_view:
      hours: '%{count} hours'
      status: "STATUS"
      total: "Total"
      phase_total: "Total - %{phase}"

    ground_time_view:
      hours: '%{count} hours'
      status: "STATUS"
      total: "Total"
      phase_total: "Total - %{phase}"
    errors:
      cbta_mismatch: CBTA cannot be enabled on a program revision if CBTA is not enabled for the account.
      has_theory_changed: "Cannot edit theory training on revision"
      has_flight_changed: "Cannot edit flight training on revision"
    form_ground:
      include_in_progression: "Below you can control whether the registered duration of a completed progress test, theory release, exam or type questionnaire should count into the class theory progression."
      include_progress_tests_in_progression: Progress tests
      include_theory_releases_in_progression: Theory releases
      include_theory_exams_in_progression: Theory exams
      include_type_questionnaires_in_progression: Type questionnaires
    show_ground:
      add_subject_category: Create subject in '%{course}'
      confirm_deactivate: Are you sure, that you want to deactivate '%{name}'?
      confirm_delete: Are you sure, that you want to remove '%{name}'?
      delete: Delete
      clone: Clone
      confirm_clone: Are you sure, that you want to clone '%{name}'?
  programs:
    aircraft_types:
      airplane: "Airplane"
      helicopter: "Helicopter"
    name: "Programs"
    flash_cloned: The program has been cloned
    flash_cloning_started: Program cloning has begun
    flash_cloning: The program is being cloned
    flash_destroyed: The program has been disabled
    flash_creating_master_list: We are creating a masterlist for the program - please check back again later
    flash_removing_master_list: We are removing the masterlist for the program - please check back again later
    flash_no_revisions: An empty program cannot be cloned
    flash_created: The program has been created. You can now add a revision.
    edit:
      unconverted_cbta_revisions: |
        This program has un-converted CBTA revisions.
        These cannot be selected as the active revision, before all exercises have been moved into flight phases.
      cbta_revisions_missing_norm: |
        This program has CBTA revisions with exercises without a norm.
        These cannot be selected as the active revision, before a norm has been assigned to all exercises.
    form:
      active_revision: Active revision
      use_master_list_from_program: Use master exercise list from another program
      clone_from_program_id: Program
      ground_training: Ground training
    index:
      active: Active
      deactivated: Deactivated
      name: Name
      active_revision: Active revision
      connected_students: Students (active/all)
      new_program: New Program
      old_programs: Deactivated programs
      title: Programs
      flight_phases: Flight Phases
      core_compentencies: Core Competencies & Performance Indicators
      cbta_matrix: CBTA Matrix
      create_new_program: Create
      header_cbta: CBTA
      header_cbta_title: Programs with CBTA compliant revisions
      cbta_status_not_converted_title: Has unconverted CBTA revisions
      cbta_status_missing_norm_title: A CBTA revision has exercises without norm
      cbta_status_compliant_title: Program has a CBTA compliant revision
      create_new_theory_program: "Ground training"
      create_new_flight_program: "Flight training"
    old:
      active_programs: Active programs
      title: Deactivated programs
    program:
      confirm_clone_program: "Are you sure, that you would like to clone program: %{program_name}?"
      confirm_disable_program: "Are you sure, that you would like to deactivate program: %{program_name}?"
    show:
      connected_students: Students (active/all)
      create_new_revision: Create revision
      go_back_to_programs: Return to programs
      create_new_master_list: Create master exercise list
      edit_master_list: Master exercise list
      remove_master_list: Remove master exercise list
      revisions_to_program: "Revisions for program: %{program}"
      name: Name
      note: Note
      header_cbta: CBTA
      header_cbta_title: Revisions that are CBTA compliant
      cbta_status_not_converted_title: CBTA enabled but revision not fully converted
      cbta_status_missing_norm_title: Has exercises without norm
      cbta_status_compliant_title: Revision is CBTA compliant
  program_phases:
    flash_destroyed: The phase has been deleted
    form:
      submit: Save
    new:
      title: Create phase
    edit:
      title: Edit phase
  progress_tests:
    flash_created: The test has been saved
    flash_destroyed: The progress test has been deleted
    flash_updated: The test is updated
    index:
      confirm: Are you sure you want to delete this progress test?
      add: Add Progress test
      date: Date
      delete: Delete
      grade: Grade
      instructor: Instructor
      subject: Lesson
      update: Edit
      category: Category
      headline: "Progress tests"
      list_empty: "There are no progress tests yet"
    form:
      add_comment: Add Student comment
  rentals:
    edit:
      title: Edit flight
    flash_created: The rental flight has been created
    flash_destroyed: The rental flight has been deleted
    flash_updated: The rental flight has been updated
    form:
      save_flight: Save rental
    index:
      block: Block
      confirm_delete: Are you sure you want to delete the rental?
      date: Date
      delete: Delete
      enter_rental: Create rental
      no_rental: You have not rented an aircraft yet
      plane: Aircraft
      rental_info: As a renter of the schools aircraft, you need to enter details
        about your rental flights here.
      rentals: Rentals
      new_rental: Fly rental
      tacho: Timer
      update: Edit
    new:
      title: Create flight
    rentals:
      title: Rentals
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      confirm_delete: Are you sure you want to delete the rental?
      date: Date
      delete: Delete
      no_rental: You have not rented an aircraft yet
      plane: Aircraft
      update: Edit
    rental_modal:
      header: Additional information
      attachments: Attachments
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      comment: Debriefing comment
      date: Date
      in: in
      landings: Landings
      name: Renter
      plane: Aircraft
      briefing_comment: Briefing comment
      departure: Departure
      arrival: Arrival
      tracks: Track(s)
      fuel_added: Fuel added
      departure_fuel: Departure fuel
      calculated_fuel: Calculated fuel
  renter_configuration:
    index:
      title: Certificate requirements
      ftl_title: Flight time limitation requirements
      update: Update requirements
  reports:
    shared:
      error: Report creation failed
    flight_time_warning:
      title: flight and duty time warnings
      title_with_count: "Flight and duty time warnings (%{count})"
      user_first_name: First name
      user_last_name: Last name
      limitation: Limitation
      type: Warning
      status: Status
      flown_seconds: Hours
      seconds_left: Hours left
      landings: Landings
      landings_left: Landings left
      flights: Flights
      flights_left: Flights left
    departure_arrival_history:
      title: Departure arrival history
      title_with_count: "Departure arrival history checkins (%{count})"
      aircraft: AIRCRAFT
      departure: DEPARTURE
      type: TYPE
      student_renter: STU/REN/CUS
      instructor_crew: INS/CREW
      observers: OBSERVERS
      pob: POB
      dispatched: dispatched
      fuel: DEP. FUEL
      arrival: ARRIVAL
      comment: COMMENT
      status: STATUS
      checkin_out: CHECKIN/OUT
      off_block: PRIMARY START
      on_block: PRIMARY END
      landed: SECONDARY END
      takeoff: SECONDARY START
      approved_status: APPROVAL
      movements: MOVEMENTS
      eta: ETA
      block: PRIMARY
      airborne: SECONDARY
    cancelled_booking:
      report_for: Cancellation report for %{from} - %{to}
    certificate_warning:
      title: Certificate warnings
      title_with_count: "Certificate warnings (%{count})"
      first_name: First name
      last_name: Last name
      days_to_expiry: Days to expiry
      certificate_name: Warning
      status: Status
      expiry: Expiry
      approved: Approved
      approved_by: By
      statuses:
        missing: Missing
        expired: Expired
        to_approval: Requiring approval
        approved: Approved
        unapproved: Unapproved
        current: Current
    changelogs:
      bookings:
        report_for: "Changelog Booking report for %{from} - %{to}"
        report_generation_job_failed: "Changelog %{report_format} report generation failed. Please try again in few minutes"
      user_programs:
        report_for: "Changelog User Program report for %{from} - %{to}"
        report_generation_job_failed: "Changelog %{report_format} report generation failed. Please try again in few minutes"
        table:
          latest_change: Latest change
          enrollment: Enrollment
          student: Student
          program_name: Program
          revision: Revision
          updated_by: Updated by
          status: Status
      tables:
        start: Start
        end: End
        booking_status: Booking Status
        type: Type
        booking_type: Booking Type
        booking_types:
          rental: Rental
          lecture: Training
          theory: Ground Training
          maintenance: Maintenance
          activity: Operation
          theory_multiple: Ground Training
          progress_test: Ground Training
          theory_release: Ground Training
          theory_exam: Ground Training
          type_questionnaire: Ground Training
          crew_lecture: Training
          meeting: Meeting
        flight_start: Flight Start
        flight_end: Flight End
        status: Status
        aircraft: Aircraft
        instructor: Instructor
        students: Students
        lesson: Lesson
        pic: PIC
        customer: Customer
        operation: Operation
        renter: Renter
        classroom: Classroom
        class_student: Class or student
        program: Program
        aircraft_classroom: Aircraft or classroom
    ground_school_warning:
      title: Ground training warnings
      title_with_count: "Ground training warnings (%{count})"
      first_name: First name
      last_name: Last name
      theory_course_name: Program
      subject_category_name: Subject
      warning_type: Warning
      issue: Status
    access_permission_report:
      title: Access permission report
      report_generation_job_failed: "Permission %{report_format} report generation failed. Please try again in few minutes"
      table:
        name: Name
        call_sign: Call sign
        user_id: User ID
    instructor: Instructor
    instructor_report:
      booking_type: Type
      cancelled: Cancelled bookings
      class: Class
      comment: Comment
      course: Course
      date: Date
      multi_engine: ME
      real_time: Time
      reason: Reason
      single_engine: SE
      student: Student
      team_students: Class/students
      theory_single: Extra theory
      theory_multiple: Class theory
      extra_theory: Extra theory
      time: Time
      total: Total
      what_is_the_invoice_number: What is the invoice number?
    invoicing:
      loading_text: It can take a while to generate this report. Please have patience
      expenses: Expenses
      find_report: Find report
      income: Income
      include_invoiced: Include invoiced
      title: Invoice report
    landings:
      mark_all: Mark all
      total: '%{landings}'
    plane: Aircraft
    briefing: Briefing
    flight:
      report_for: Flight report for %{from} - %{to}
    theory:
      report_for: Theory report for %{from} - %{to}
    production:
      aircrafts: Call sign
      find_report: Find report
      programs: Activities
      report_for: Production report for %{from} - %{to}
    program:
      total: '%{flight_time}'
    program_revision:
      title: Program revision report - %{program} (%{revision})
    program_revision_time:
      title: Program revision time report - %{program} (%{revision})
    rental_flights:
      invoice_marked: Invoice marked
      quickbooks: "QuickBooks"
    simulator_flights: Simulator Flights
    student: Student
    fuel:
      flightable_types:
        user_lecture: School flight
        rental: Rental
        trip: Operation
      measurement:
        liters: L
        USG: USG
        qt: qt
      unit:
        timer: Timer
        block: Block
        tacho: Timer
        airborne: Airborne
      empty_flights: "There are no flights yet"
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      block: Block
      date: Date
      fuel: Calculated fuel
      fuel_added: Fuel added
      oil_added: Oil added
      total_fuel: Departure fuel
      fuel_coefficient: Fuel coefficient
      oil_coefficient: Oil consumption
      airborne: Airborne
      plane: Aircraft
      departure: Departure
      arrival: Arrival
      ins_crew: Ins/Crew
      stu_ren_cus: Stu/Ren/Cus
      flight_id: Flight ID
      report_for: Fuel report from %{from} - %{to}
      timer: Timer
      title: Fuel report
      total: Total
      type: Type
    user_program_total:
      landings: Landings
      lesson: Lesson
      programme_for: 'Student: %{student}'
      total: Total
    user_report:
      advanced_search: Advanced Search
      airport: Airport
      approach: Approach
      cancelled_bookings: Cancelled booking
      comment: Comment
      course: Subject
      customer: Customer
      date: Date
      full_stop: Full stop
      go_arounds: Go-around
      instructor: Instructor
      examiner: Examiner
      invoice: Invoice
      invoice_marked: Invoice
      invoice_number: Invoice number
      landings: Landing
      mark_all: Mark all
      multi_engine: ME
      plane: Aircraft
      real_time: Time
      reason: Reason
      rentals: Rental
      schoolflight: School flight
      single_engine: SE
      student: Student
      tacho: Timer
      team_students: Class/students
      theory_single: Extra theory
      theory_multiple: Class theory
      training: Training
      extra_theory: Extra theory
      duty_time: Duty time
      primary_time: Primary
      secondary_time: Secondary
      tertiary_time: Tertiary
      total: Total
      total_amount: In total
      touch_and_go: Touch and go
      type: Type
      update: Edit
      what_is_the_invoice_number: What is the invoice number?
      student_or_customer: Student/Class/Customer
      description: Description
      category: Category
      day_night_landing: Day/Night
      day: Day
      night: Night
      progress_tests: Progress tests
      theory_releases: Theory releases
      theory_exams: Exams
      type_questionnaires: Type questionnaires
    user_accounting_report:
      invoiced_at: Invoiced at
      kind_of_operation: Kind of operation
      status: Status
      payment_status_changed_at: Payment status changed at
      payment_method: Payment method
      total: Total
      receipt: Receipt
      title: User Accounting Report for %{from} - %{to}
      customer_title: Customer Accounting Report for %{from} - %{to}
      report_generation_job_failed: "User accounting %{report_format} report generation failed. Please try again in few minutes"
    customer_accounting_report:
      report_generation_job_failed: "Customer accounting %{report_format} report generation failed. Please try again in few minutes"
    user_total:
      airport: Airport
      amount: Amount
      block_time: Block time
      date: Date
      dual_time: Dual time
      hours_flown: Hours flown
      instructor: Instructor
      landings: Landings
      lesson: Lesson
      plane: Aircraft
      rentals: Rentals
      solo_time: Solo time
      student: 'Student: %{user}'
      tacho: Timer
      theory: Theory
      time: Time
    maintenance_warning:
      title: "Maintenance warnings"
      title_with_count: "Maintenance warnings (%{count})"
      call_sign: Call sign
      model: Model
      maintenance_part: Warning
      status: Status
      days_left: Days left
      timer_time_left: Log time left
      airborne_time_left: Airborne time left
      cycles_left: Cycles left
      expiry: Expiry
    accounting_transactions:
      report_for: Accounting transactions for %{from} - %{to}
    user_exercise_progress:
      title: Exercise tracker - %{name}
      all_phases: All phases
      column_header:
        name: Exercise tracker
        highest_achieved_grade: Highest achieved grade
        highest_required_norm: Highest required norm
        count: Exercise count
    user:
      user_program_reports:
        error: Failed generating report, please try again later or contact support
      payment_reports:
        error: Failed generating report, please try again later or contact support
  shared:
    error_messages:
      could_not_be_saved: Your action could not be completed
    report_form:
      find_report: Find report
      mark_all: Mark all
      include_invoiced: Include invoiced
      print: Print
    offline_notification:
      header: It looks like you're offline.
      explanation: The page will continue to work but you won't be able to save until you are back online.
    attachments_viewer:
      open_link: Open in new tab
    save_credit_card:
      update_card_info: 'Adding a new credit card will replace your current credit card: '
    theory:
      attendance_mark_all_students:
        mark_all_students: Mark all "Attended"
        submit: Submit
        edit: Save
      attendance_status_form:
        attendance_status: Attendance
        attended: Attended
        partially_attended: Attended partially
        did_not_attend: Didn't attend
      starts_ends_at:
        unknown: Unknown
        start_unknown: 'Start: Unknown. End:'
        end_unknown: 'End: Unknown.'
      attendance_tab:
        mark_all_attended: Mark all "Attended"
        add_student: Add student
      nested_student_form:
        attendance_status: Attendance
        attended: Attended
        partially_attended: Partially attended
        did_not_attend: Didn't attend
        inactive_program_student: Student is not active on %{program}, and will not be registered
        inactive_revision_student: Student is not active on this %{program} revision, and will not be registered
        not_accessible_student: Student is not accessible to you and will not be registered
        ref_num: Ref. number
      debriefing_tab:
        reports-and-messages: Reports and messages
        button-send-feedback: Use the button below to send feedback
        feedback: Feedback
        safety_management: Safety Management
        debriefing_comment: Debriefing comment
    report_new_form:
      include_invoiced: Include invoiced
    flight_attachments:
      tracked_flight_title:
        one: There is 1 flight track attached to this flight registration.
        other: There are %{count} flight tracks attached to this flight registration.
      document_title:
        one: There is 1 document attached to this flight registration.
        other: There are %{count} documents attached to this flight registration.
  show_add_emergency_contects: Add emergency contact
  sittings:
    sitting:
      confirm: Are you sure you would like to delete this?
      delete: Delete
      edit: Edit
      date: Date
      grade: Grade
      instructor: Instructor
      ref_num: Ref. number
      course: Program
      new_sitting: Create new sitting
      attempts: Attempts
      category: Subject
  announcement_modal:
    contributor_section:
      contributors: 'CONTRIBUTORS:'
    contributors:
      additional:
        one: "+ %{count} additional client contributed to this release for you"
        other: "+ %{count} additional clients contributed to this release for you"

  sitting_groups:
    changer: Change state
    disabled: Completed
    enabled: Active
    discontinued: Discontinued
    show:
      new_sitting: Add Sitting
      new_exam_in_sitting: Add Exam
      confirm: Are you sure you would like to delete this?
    nav:
      new_sitting_group: Add Sitting group
      state: 'Sitting group status:'
      headline: "Sittings"
      list_empty: "There are no sittings yet"
    index:
      headline: "Exams"
      list_empty: "There are no exams yet"
  special_theory_lectures:
    form:
      submit: Submit
      edit: Submit changes
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this extra theory?
      move_program_warning: Please note moving this extra theory to another program cannot be undone or changed later!
    flash_created: Extra theory has been created
    flash_destroyed: Extra theory has been deleted
    index:
      confirm: Are you sure you want to delete this extra theory?
      date: Date
      delete: Delete
      description: Description
      extra_theory: Extra theory
      new_special_theory_lecture: Add Extra theory
      no_special_theory_lectures: "There are no extra theory yet"
      time: Time
    instructor_invoice_form:
      save: Save
    invoice_form:
      save: Save
    new:
      save: Complete extra theory
      title: New extra theory
    edit:
      title: Edit extra theory
      save: Submit changes
    flash_updated: "Extra theory has been updated"
    show:
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this extra theory?
    special_theory_lecture:
      title: Extra theory
      instructor: Instructor
      description: Description
      comment: Comment
      update: Edit
    report:
      title: "Extra theory report on %{date}"
      empty_description: "There is no description"
  subject_categories:
    form:
      hint: Eg. Aerodynamics or meteorology
      total_time: Total time
    index:
      create_subject_category: Create Theory category
      old_categories: Deactivated theory categories
      title: Theory categories
    old:
      categories: Active theory categories
      title: Deactivated theory categories
    subject_category_table:
      activate: Activate
      deactivate: Deactivate
      name: Name
    flash_cannot_be_cloned: "Unable to clone theory category"
    flash_cloned: "Theory category cloned"
    flash_cannot_delete: "Unable to delete theory category"
    flashed_deleted: "Theory category has been deleted"
  theory_courses:
    flash_cloned: The theory couse has been cloned
    flash_clone_not_possible: Theory course cannot be cloned
    cannot_destroy: Unable to destroy while categories are still connected
    form:
      ground_school_requirement: Ground training warnings
      include_in_progression: Below you can control whether the registered duration of a completed progress test, theory release, exam or type questionnaire should count into the class theory progression.
      include_progress_tests_in_progression: Progress tests
      include_theory_releases_in_progression: Theory releases
      include_theory_exams_in_progression: Theory exams
      include_type_questionnaires_in_progression: Type questionnaires
    index:
      active: Active
      deactivated: Deactivated
      old_courses: Deactivated theory courses
      title: Theory courses
    old:
      active_courses: Active theory courses
      title: Theory courses - deactivated
    theory_course:
      add_subject_category: Create Theory category in '%{course}'
      confirm_deactivate: Are you sure, that you want to deactivate '%{name}'?
      confirm_delete: Are you sure, that you want to remove '%{name}'?
      delete: Delete
      clone: Clone
      confirm_clone: Are you sure, that you want to clone '%{name}'?
  team_progress_tests:
    edit:
      title: Edit Progress test
      submit: Submit changes
    flash_created: The progress test has been created
    flash_updated: The progress test is updated
    form:
      submit: Submit
      add_student: Add student
      add_comment: Add Student comment
      edit: Submit changes
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this progress test?
      mark_all_students: 'Mark all "Attended"'
      attendance_status: Attendance
      attended: Attended
      partially_attended: Attended partially
      did_not_attend: Didn't attend
      info_legacy_theory: Progress tests created before the introduction of start/end times cannot have a duration.
      inactive_student: Student is inactive, and will not be registered
      theory_type: "Progress test"
    new:
      title: Add Progress test
    show:
      comment: Comment
      date: Date
      grade: Grade
      instructor: Instructor
      progress_test_in: 'Progress test'
      student: Student
      subject: "Subject"
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this progress test?
    progress_tests:
      comment: Comment
      instructor: Instructor
      subject: Note
      subject_category: Subject
      test_date: Date
      user: Student
  team_theory:
    category: Category
    confirm_delete: Are you sure you want to delete this class theory?
    date: Date
    edit: Edit
    instructor: Instructor
    progress_bar_title: "Complete overview %{percentage}% complete %{completed} / %{total} hours"
    subject: Lesson
    subject_category: Subject
    duration: Duration
    missing_starts_at: Unknown
    lesson_note: "Lesson/Note"
    type: "Type"
  team_theory_exams:
    choose_sitting: Choose sitting
    create_new_sitting: + Add sitting
    create_new_sitting_group: Create new sitting group
    deactivated: Deactivated
    edit:
      title: Edit Exam
      submit: Submit changes
    flash_created: Exam created
    flash_updated: Exam updated
    flash_destroyed: Exam deleted
    form:
      add_comment: Add Student comment
      add_student: Add student
      submit: Submit
      inactive_student: Student is inactive, and will not be registered
      edit: Submit changes
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this exam?
      info_legacy_theory: Exams created before the introduction of start/end times cannot have a duration.
      theory_type: "Exam"
    new:
      title: Add Exam
    show:
      date: Date
      exam_in: 'Exam'
      examiner: Examiner
      grade: Grade
      sitting: Sitting
      student: Student
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this exam?
    exam_index:
      exams: Exams
      examiner: Examiner
      subject_category: Subject
      ground_training_program: Ground training program
      date: Date
      edit: Edit
      confirm_delete: Are you sure you want to delete this exam?
      list_empty: "There are no exams yet"
      duration: Duration
      headline: "Exams"
    index:
      new_exam: Add Exam
      update: "Edit"
    exams:
      examiner: Examiner
  team_theory_releases:
    edit:
      title: Edit Theory release
      submit: Submit changes
    flash_created: The theory release has been created
    flash_updated: Theory release is updated
    form:
      submit: Submit
      add_comment: Add Student comment
      add_student: Add student
      inactive_student: Student is inactive, and will not be registered
      edit: Submit changes
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this theory release?
      info_legacy_theory: Theory releases created before the introduction of start/end times cannot have a duration.
      theory_type: "Theory release"
    new:
      title: Add Theory release
    theory_releases:
      comment: Comment
      date: Date
      grade: Grade
      instructor: Instructor
      student: Student
      theory_release: 'Theory Release'
      subject: Note
    show:
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this theory release?
  teams:
    subject_category_theory_lectures:
    theory:
      subject: Lesson
      subject_category: Subject
      date: Date
      instructor: Instructor
      edit: Edit
      category: Subject
      progress_bar_title: "%{percentage}% completed"
      progress_bar_of: " (%{completed} of %{total})"
      confirm_delete: Are you sure you want to delete this class theory?
      headline: "Class theory"
      list_empty: "There are no class theory yet"
    all_progress_tests:
      subject: Note
      subject_category: Subject
      new_progress_test: Add Progress test
      date: Date
      duration: Duration
      instructor: Instructor
      edit: Edit
      category: Category
      confirm_delete: Are you sure you want to delete this progress test?
      headline: "Progress tests"
      list_empty: "There are no progress tests yet"
    all_theory_releases:
      subject: Note
      subject_category: Subject
      new_theory_release: Add Theory release
      date: Date
      instructor: Instructor
      edit: Edit
      confirm_delete: Are you sure you want to delete this theory release?
      headline: "Theory releases"
      list_empty: "There are no theory releases yet"
      duration: Duration
    all_type_questionnaires:
      subject: Note
      subject_category: Subject
      new_type_questionnaire: Add Type questionnaire
      date: Date
      instructor: Instructor
      edit: Edit
      confirm_delete: Are you sure you want to delete this type questionnaire?
      headline: "Type questionnaire"
      list_empty: "There are no type questionnaire yet"
      duration: Duration
    edit:
      title: Edit class
    flash_activated: The class has been activated
    flash_created: The class has been created
    flash_destroyed: The class has been deactivated
    flash_updated: The class has been updated
    form:
      submit: Save class
    index:
      submit: Create class
      teams: Classes
      active: Active
      deactivated: Deactivated
      name: Name
      no_active_teams: "There are no active classes to be shown at the moment"
      no_deactivated_teams: "There are no deactivated classes to be shown at the moment"
    new:
      title: Create new class
    nav:
      students: Students
      type_questionnaires: Type questionnaires
      theory: Progress
      progress_tests: Progress tests
      theory_releases: Theory releases
      exams: Exams
      cancelled_bookings: Cancelled bookings
    students:
      add_student_to: Add students to class - %{team}
    team_table:
      name: Name
      students: Students attached
    student_table:
      students: Students
      name: Name
      list_empty: "There are no students yet"
      ground_training: Ground training program
      not_active: Not active
      remove: Remove student from class
      confirm: Are you sure you want to remove the student from the class?
    theory_exams:
    ref_num: Ref. number
    team:
      confirm_disable_team: "Are you sure, that you want to deactivate the class: %{team_name}?"
  user_neutralize:
    index:
      title: GDPR neutralize user
      header: GDPR neutralization of a user is only possible with the following checked
      explanation: GDPR neutralization results in inability to recognize the user in all FlightLogger records. The action is irreversible, so first make sure you have saved any necessary documentation.
      no_bookings: No future bookings
      no_programs: No active programs
      neutralize_confirm: Are you sure you want to GDPR neutralize this user?
      neutralize_user: GDPR neutralize user
      password_prompt: Enter your password to confirm the GDPR neutralization
      neutralize_header: Confirm GDPR neutralization
      neutralize_user_message: "Note the unique id: [%{id}] before GDPR neutralization."
    flight_reasons:
      flightable_type: Type
      no_flights: The user has no registered flights.
    program_reasons:
      no_programs: The user has no registered active programs.
    booking_reasons:
      no_bookings: The user has no registered future bookings.
  user_delete:
    logbook_entries: Logbook entries
    role: Role
    transactions: Transactions
    theories: Theories
    date: Date
    name: Name
    type: Type
    status: Status
    approval_status: Approval status
    count: Count
    bookings: Bookings
    flights: Flights
    messages: Messages
    programs: Programs
    logbook_entry_reasons:
      no_logbook_entries: The user has no registered logbook entries
      remarks_and_endorsements: Remarks and endorsements
    theory_reasons:
      type_questionnaire: Type questionnaire
      progress_test: Progress test
      theory_lecture: Theory lecture
      theory_exam: Theory exam
      theory_release: Theory release
      special_theory_lecture: Special theory lecture
      no_order_items: The user has no transactions
    program_reasons:
      no_programs: The user has no registered programs.
    index:
      no_bookings: No bookings
      delete_user: Delete user
      title: Delete user
      delete_header: Deletion of a user is only possible with the following checked
      no_flights: No flights
      no_messages: No messages
      no_programs: No Programs - (active, completed, discontinued and standby)
      no_theories: No theory registrations
      no_logbook: No logbook entries
      no_instructor_activities: No instructor activities
      no_certificate_admin_activities: No certificate admin activities
      no_maintenance_admin_activities: No maintenance admin activities
      deletion_explanation: The deletion of a user is irreversible and permanent, make sure this is what you want.
      deletion_requirement_reason: The deletion of a user is irreversible. If you have any requirements that are not checked, we recommend to GDPR neutralize instead.
      no_transactions: No accounting transactions
      delete_confirm: Are you sure you want to delete this user?
    booking_reasons:
      no_bookings: The user has no registered bookings.
    order_items_reasons:
      no_order_items: The user has no registered transactions
    message_reasons:
      no_messages: The user has no registered messages.
      creator: Creator
    flight_reasons:
      flightable_type: Type
      no_flights: The user has no registered flights.
  theory_lectures:
    edit:
      title: Edit Class theory
      form:
        submit: Submit changes
    flash_created: Class theory has been created
    flash_updated: Class theory has been updated
    flash_destroyed: Class theory has been removed
    flash_no_booking: Class theory can only be created if a booking is present.
    form:
      add_student: Add student
      add_comment: Add Student comment
      attended: Attended
      did_not_attend: Didn't attend
      mark_all_students: 'Mark all "Attended"'
      inactive_student: Student is inactive, and will not be registered
      partially_attended: Attended partially
      attendance_status: Attendance
      submit: Submit
      edit: Submit changes
      confirm_destroy: Are you sure you want to delete this class theory?
      destroy: Delete
      theory_type: "Class theory"
    index:
      add: Add class theory
      confirm: Are you sure you would like to delete this class theory?
      date: Date
      instructor: Instructor
      lectures: Class theory - Completed
      open_bookings: Class theory - Open bookings
      subject: Lesson
      team: Class
      today: Today
      category: Category
      duration: Duration
      total: Total
    instructor_invoice_form:
      save: Save
    new:
      title: Add class theory
    theory_lecture_participants:
      attendants: Class theory
      comment: Comment
      student_comment: Student comment
      student: Student
      update: Edit
      subject: Lesson
      list_empty: "There is no comment on this registration"
      instructor: "Instructor"
    show:
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this class theory?
  theory_releases:
    flash_created: The theory release has been created
    flash_destroyed: The theory release has been deleted
    flash_updated: The theory release has been updated
    index:
      add_release: Add Theory release
      category: Category
      headline: "Theory releases"
      list_empty: "There are no theory releases yet"
    form:
      add_comment: Add Student comment
  time:
    am: am
    pm: pm
  trips:
    edit:
      title: Edit a %{activity}
    flash_created: The operation has been created
    flash_destroyed: The operation has been deleted
    flash_updated: The operation has been updated
    form:
      add_crew: Add Crew
      add_flight: Add flight
      choose_customer: Choose a customer
      choose_customer_or_create_new: Select a customer or %{link}
      create_new_customer: create a new
      note: Note
      submit: Save operation
    instructor_invoice_form:
      save: Save
    new:
      title: Fly a %{activity}
    trip:
      title: Additional information
      activity_type: Operation type
      attachments: Attachments
      address: Address
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      city: City
      briefing_comment: Briefing comment
      comment: Debriefing comment
      company: Company
      customer: Customer
      operation: Operation
      departure: Departure
      arrival: Arrival
      tracks: Track(s)
      date: Date
      in: in
      pic: PIC
      crew: Crew
      landings: Landings
      name: Name
      phone: Telephone
      plane: Aircraft
      country: Country
      fuel_added: Fuel added
      departure_fuel: Departure fuel
      calculated_fuel: Calculated fuel
  type_questionnaires:
    flash_created: Type questionnaire created
    flash_destroyed: Type questionnaire deleted
    flash_updated: Type questionnaire updated
    index:
      confirm: Are you sure you want to delete this type questionnaire?
      add_questionnaires: Add Type questionnaire
      date: Date
      title: Type questionnaires
      type: Type
      no_questionnaires: There are no type questionnaires yet
      student_comment: Student comment
      category: Category
      subject: Note
      attendance: Attendance / Duration
      grade: Grade
      no_category: Unknown category
    form:
      plane_type: Type
      review_date: Date
      add_comment: Add Student comment
      subject: Lesson
    show:
      title: Type questionnaire
      instructor: Instructor
      student: Student
      student_comment: Student comment
      subject: Lesson
      update: Edit
  team_type_questionnaires:
    questionnaires:
      title: Type questionnaire
      date: Date
      student: Student
      subject: Lesson
      instructor: Instructor
      student_comment: Student comment
      comment: Comment
      list_empty: "There are no type questionnaires"
      no_comment: "There are no comment yet"
      no_subject: "There are no subject yet"
    edit:
      title: Edit Type questionnaire
    new:
      title: Add Type questionnaire
    index:
      add_questionnaires: Add Type questionnaire
      confirm: Are you sure you want to delete this type questionnaire?
      date: Date
      title: Type questionnaires
      subject: Lesson
      no_questionnaires: There are no questionnaires yet
      category: "Category"
      list_empty: "There are no type questionnaires yet"
      confirm_delete: Are you sure you want to delete this type questionnaire?
      duration: Duration
    form:
      add_comment: Add Student comment
      add_student: Add student
      subject: Lesson
      review_date: Date
      submit: Submit
      inactive_student: Student is inactive, and will not be registered
      edit: Submit changes
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this type questionnaire?
      info_legacy_theory: Type questionnaires created before the introduction of start/end times cannot have a duration.
      theory_type: "Type questionnaire"
    team_type_questionnaire:
      add_questionnaires: Add Type questionnaire
      confirm: Are you sure you want to delete this type questionnaire?
      date: Date
      title: Type questionnaires
      subject: Lesson
      no_questionnaires: There are no questionnaires yet
      list_empty: "There are no type questionnaires"
      category: "Category"
      type: Type
      confirm_delete: Are you sure you want to delete this type questionnaire?
    show:
      title: Type questionnaires
      subject: "Subject"
      instructor: "Instructor"
      comment: "Comment"
      student: "Student"
      list_empty: "There are no type questionnaires yet"
      update: Edit
      destroy: Delete
      confirm_destroy: Are you sure you want to delete this type questionnaire?
      exam_in: Foobar
    report:
      title: "Type questionnaire report on %{date}"
  user_documents:
    breadcrumbs:
      documents: Documents
    edit:
      title: Edit document
    flash_created: The document has been uploaded
    flash_destroyed: The document has been deleted
    flash_updated: The document has been updated
    folders:
      confirm: Are you sure you want to delete the folder
      visible_to_user_hint: This folder is not visible for the user
      visible_to_other_hint: This folder is not visible for other users than administrators
    documents:
      confirm: "Are you sure you want to delete this document?\nThis can not be undone!"
      no_documents: There are no documents yet
      visible_to_user_hint: This document is not visible for the user
      visible_to_other_hint: This document is not visible for other users than administrators
    index:
      confirm: Are you sure you want to delete the document
      delete: Delete
      documents: Documents
      name: Name
      new_document: Upload documents
      new_folder: New folder
      update: Edit
      modified: Modified
      uploaded_at: Uploaded at
      uploaded_by: Uploaded by
      no_documents: There are no documents yet
    new:
      title: Upload new document
  user_document_folders:
    edit:
      title: Edit folder
      no_file: No file chosen
      choose_file: Choose file
    flash_cant_destroy: The folder can't be deleted, because it contains files.
    flash_created: The folder is created
    flash_destroyed: The folder is deleted
    flash_updated: The folder is updated
    new:
      title: Create folder
      no_file: No file chosen
      choose_file: Choose file
    show:
      confirm_delete: Are you sure you want to delete this folder?
      delete: Delete
      documents_in: 'Documents in '
      name: Name
      members: Members
      update: Edit
  user_lectures:
    reply_title: "Regarding: %{lesson_name} flown %{time}"
    cant_show_user_custom_lecture: Extra lesson cannot be shown in view mode
    cant_edit_flown_user_custom_lecture: A flown extra lesson cannot be edited
    cant_edit_approved_lecture: The lesson have been approved by the student, and can't be edited
    cant_edit_neutralized: The user of the lesson is neutralized, and can't be edited
    student_is_instructor: and student cannot be the same person
    status_form:
      repetition_needed_part1: Repetition lesson needed,
      repetition_needed_part2: '-exercise will be transfered'
      repetition_no_flags: Repetition lesson needed, all exercises will be transfered
      passed: Passed
      failed: Failed
    show_exercise:
      grade_description: This is the highest given grade
    form:
      grade_description: This is the highest given grade
      pass_warning: You have flagged some exercises. Are you sure you want to save without creating a repetition lesson?
      rep_warning: You have not flagged any exercises. Are you sure you want to save and create a repetition lesson?
      add_extra_exercise: add extra exercise
      exercises: Exercises
      flights: Flights
      parent_user_lecture: Position
      submit: Save lesson
    edit_right_expired: Right to edit lecture expired
    flash_approved: The lesson has been approved
    flash_reset: The lesson has been reset
    flash_created: The custom lesson has been created
    flash_deleted: The lesson has been deleted
    flash_updated: The lesson has been updated
    flash_error_not_found: "The lesson was not saved!!\nDuring your registration, the lesson was removed by another user."
    flash_error_exercises_changed: "The lesson was not saved!!\nDuring your registration, the content of the lesson was edited by another user."
    navigate_lecture:
      next_lesson: Next lesson
      prev_lesson: Prevous lesson
    lecture_info:
      custom_title: Edit custom lesson
      custom_title_new: New custom lesson
      asymmetric_time: 'AT: '
      confirm_reset: Are you sure you want to reset the lesson?
      confirm_delete: Are you sure you want to delete this lesson?
      cross_country: 'XC: '
      duration: Duration
      ifr_dual_time: 'IFR dual: '
      ifr_sim_time: 'IFR sim: '
      ifr_spic_time: 'IFR SPIC: '
      lesson_desc: Lesson description
      if_time: 'IF: '
      me_time: 'Multi-engine: '
      night_time: 'Night: '
      reset: Reset
      delete: Delete
      pilot_flying_time: 'PF: '
      pilot_monitoring_time: 'PM: '
      vfr_dual_time: 'VFR dual: '
      vfr_sim_time: 'VFR sim: '
      vfr_solo_time: 'VFR solo: '
      vfr_spic_time: 'VFR SPIC: '
      last_flown_lesson: 'View last flown lesson'
      asymmetric: 'AT: '
      ifr_dual: 'IFR dual: '
      ifr_sim: 'IFR sim: '
      ifr_spic: 'IFR SPIC: '
      instrument: 'IF: '
      multi_engine: 'Multi-engine: '
      night: 'Night: '
      pilot_flying: 'PF: '
      pilot_monitoring: 'PM: '
      vfr_dual: 'VFR dual: '
      vfr_sim: 'VFR sim: '
      vfr_solo: 'VFR solo: '
      vfr_spic: 'VFR SPIC: '
    show:
      contact_instructor: Contact instructor
      instructor: "Instructor:"
      student: "Student:"
      approve_lecture: Approve lesson
      approve_btn_busy: Processing
      exercises: Exercises
      name: Name
      remarks: Remarks
  student_window:
    user_hover:
      title: Flight booking
      last_days: Last %{days} days
      open: "Open:"
      completed: 'Completed:'
      cancelled: 'Cancelled:'
      total: 'Total:'
      future: Future
      last_completed: Last completed
      first_up: First up
    lesson_hover:
      duration: Duration
      additional: Additional
      pre_time: Briefing
      post_time: Debriefing
      booking_date: Booking date
      flight_date: Flight date
  user_program:
    must_have_revision: Must have revision
  user_programs:
    todo_items_paginate:
      hil: Hold item list
      lesson: Lesson
      exercise: Exercise
      date_of_completion: Date of completion
      lesson_of_completion: Lesson of completion
      grade: Grade
      comment: Comment
      instructor: Instructor
      no_todo_items: No hold items yet
      date: Date
    subgrades_paginate:
      subgrades: Subgrade
      date: Date
      exercise: Exercise
      grade: Grade
      comment: Comment
      no_subgrades: No subgrades
      lesson: Lesson
      completion_date: Date of completion
      lesson_completion: Lesson of completion
      instructor: Instructor
      highest_achived_grading: Highest achieved grading
      times_performed: Number of times this exercise has been performed
    progress_bar: "%{percent}% completed"
    edit:
      active: Active
      active_lower: active
      completed: Completed
      completed_lower: completed
      confirm_delete: Are you sure you want to remove this program from the student?
      program_cant_be_removed: All lessons must be deleted before the program can be removed from the user
      program_destroy_job_in_progress: The program is already being deleted
      program_not_removed: The program couldn't be removed from the user
      program_removed: The program is now pending removal. This action could take some time.
      remove_program: Remove program
      standby: Standby
      standby_lower: standby
      discontinued: Discontinued
      discontinued_lower: discontinued
      submit: Save
      title: Change status of the program
    flash_activated: The program has been activated
    flash_changed_state: The program has changed state
    flash_could_not_change_state: The program couldn't change state
    flash_created: The program has been added
    flash_existing: |
      The student is already %{status} on %{program} and can therefore not be reassigned.
      In order to reassign the program the currently %{status} version must be set to completed or discontinued.
    flash_destroyed: The program has been deactivated
    new:
      submit: Add new program
      title: Add new program
    no_pdf_found_error_js: An error have accuring with the PDF, please retry
    profile_header:
      asymmetric_time: AT
      cross_country: XC
      dual: Dual
      if_time: IF
      me: ME
      night: Night
      sim: Sim
      solo: Solo
      spic: SPIC
      status: STATUS
    program_remaining:
      remaining: Remaining - %{program}

    program_row:
      actual: Actual
      credited: Credited
      landings_count: "%{landings} LDG"
      landings_explanation: "Number of 'Full stop' and 'Touch & Go' performed in %{program}"
      program_active_duration_title: Day(s) from first flight until today
      program_completed_duration_title: Day(s) from first flight until last flight
      program_duration: "%{duration} D"
      syllabus: Syllabus
    program_phase:
      phase_total: Total - %{phase}
    user_program_header:
      program_state: 'Program status:'
      assignment: 'Assignment: '
    previous_experience:
      header: Credited hours
      add: Amend credited hours
      saved: Saved changes to credited hours
      save_credited_hours: Save credited hours
      reset: Reset
      syllabus: Syllabus
      credited: Credited
    progress: &user_program_show
      unfinished_hil: Unfinished HIL items
      all_hil: All HIL items
      change_state: Change state
      completed_grade: Completed / Grade
      date: Date
      day: Day
      day_time: Day time
      delete: Delete
      dont: Don't show
      grade_comment: Grade / Comment
      grade: Grade
      comment: Comment
      hil: Hold item list
      hours: hours
      instructor: Instructor
      lectures: Lessons
      lesson_exercise: Lesson / Exercise
      exercise: Exercise
      lesson: Lesson
      name: Name
      new_custom_lecture: Fly custom lesson
      new_syllabus_lecture: Add extra lesson
      night: Night
      no_subgrades: No subgrades
      no_todo_items: No hold items yet
      only: Show only
      active: Active
      completed: Completed
      standby: Standby
      discontinued: Discontinued
      show: Show
      subgrades: Subgrades
      vfr_ifr: Complete overview
      finished_in: Finished in
      lecture_row:
        fly: Proceed
        disabled_text: The student must be on an active program, in order to enable registration
        disabled_text_own_program: Unable to do registrations on a program assigned to yourself
        edit_custom: Edit extra lesson
        credited: CREDITED
        inst_dot: INST.
      phase:
        phase_total: Total - %{phase}
    user_program: *user_program_show
    detailed_header:
      quickbooks: "QuickBooks"
    member_does_not_share:
      the_student_has_currently_not_given_permission_to_see_training_programs_from_the_other_organizations: "The student has currently not given permission to see training programs from the other organizations."
      within: "Within "
      my_flight_logger_->_settings: "my|FlightLogger -> Settings "
      the_student_change_his/her_permissions: "the student can change his/her permissions."
    collaboration_does_not_exist:
      this_student_has_training_programs_from_other_flight_logger_organizations: "This student has a training program from another FlightLogger organization!"
      you_can_get_access_to_all_the_students_training_programs_by_collaborating_with_other_flight_logger_organizations: "You can get access to the training program by collaborating with the other FlightLogger organization."
      to_initiate_a_collaboration_simply_go_to: "To initiate a collaboration simply go to "
      administration_->_collaborations: "Administration -> Collaborations"
    program_collaboration_does_not_exist:
      although_you_have_a_collaboration_with_this_organisation_this_program_is_not_being_shared: "Although you have a collaboration with %{arg1}, this program or student is not being shared."
      collaborations_can_be_managed_in: "Collaborations can be managed in "
      administration_->_collaborations: "Administration -> Collaborations"
    program_overview:
      complete: completed
      program_status: Program status
      assignment_date: "Enrolled: %{program_date}"
      program_nav:
        flight_training: Flight Training
        theory_training: Ground Training
        progress: Progress
        cbta_matrix: CBTA matrix
        exercise_tracker: Exercise tracker
        class_theory: Progress
        progress_tests: Progress tests
        theory_releases: Theory releases
        type_questionnaires: Type questionnaires
        extra_theory: Extra theory
        exams: Exams
        active: Active
        completed: Completed
        deactivated: Deactivated
        add_sitting_group: Add sitting group
        sittings_full: Max sitting groups reached
      attendance: attendance
      attendance_of_total: "(%{attended} of %{total})"
      completed_total: "(%{completed} of %{total})"
    class_theory:
      disabled_text: The student must be on an active program, in order to register
      category: Subject
      attendance: Attendance
      percentage: Attendance percentage
      headline: Class theory
      list_empty: There are no class theories yet
      add_credited_hours: Amend credited hours
      theory_credits:
        category: Subject
        credited_hours: Credited hours
        syllabus: Syllabus
        credited: Attendance / Duration
        none_to_show: There are no credited hours
    progress_tests:
      confirm: Are you sure you want to delete this progress test?
      add: Add Progress test
      date: Date
      delete: Delete
      grade: Grade
      instructor: Instructor
      subject: Note
      update: Edit
      category: Subject
      headline: Progress tests
      list_empty: There are no progress tests yet
      student_comment: Student comment
      attendance: Attendance / Duration
    theory_releases:
      add_release: Add Theory release
      category: Subject
      headline: Theory releases
      list_empty: There are no theory releases yet
    type_questionnaires:
      confirm: Are you sure you want to delete this type questionnaire?
      add_questionnaires: Add Type questionnaire
      date: Date
      title: Type questionnaires
      type: Type
      no_questionnaires: There are no type questionnaires yet
      student_comment: Student comment
      category: Subject
      subject: Lesson
      attendance: Attendance / Duration
      grade: Grade
      no_category: Unknown category
    extra_theory:
      confirm: Are you sure you want to delete this extra theory?
      date: Date
      delete: Delete
      description: Description
      extra_theory: Extra theory
      new_special_theory_lecture: Add Extra theory
      no_special_theory_lectures: There are no extra theories yet
      time: Time
    sitting_groups:
      show:
        confirm_delete: Are you sure you would like to delete this?
        no_sittings_yet: There are no sittings yet
      edit:
        delete: Delete sitting group
        title: Change status of the sitting group
        state_label: Status
        submit: Save
        completed: Completed
        active: Active
        discontinued: Deactivated
        header: Change status of %{sitting_group}
    external_warning: Programs accessed through collaboration currently do not contain Ground Training
    theory_lectures_users:
      index:
        date: Date
        missing_start: Unknown
        theory_type: Type
        subject: Lesson
        lesson: "Lesson/Note"
        instructor: Instructor
        instructor_abbreviated: Inst.
        type: Type
        comment: Comment
        student_comment: Student comment
        attendance: Attendance / Duration
        total_time: Total Time
        completed_time: Completed Time
        completed_percentage: Completed Percentage
        attended_time: Attended Time
        credited: Credited
        submit: Submit
      invoice_form:
        submit: Submit
      edit:
        submit: Submit changes
    theory_credits:
      show:
        credited_hours: Credited hours
        syllabus: Syllabus
        duration: Duration
        attendance: Attendance
        category: Subject
        save_credited_hours: Save credited hours
  user_sessions:
    flash_created: You're logged in
    flash_destroyed: You're logged out
    flash_not_created: Wrong email or password, try again
    flash_inactivity: You have been logged out due to inactivity
  user_certificates:
    edit:
      title: Edit user
      update: Update requirements
  users:
    normal_contact_tab:
      pending_confirmation: Awaits user confirmation of email changing request sent out at %{sent_at}. Until
    user_form:
      date_of_birth: "Date of birth"
      email: Email
      email_edit_hint: To edit your email please go to %{link}
      email_readonly_hint: Only the user can edit their email via my|FlightLogger
      login_email: Login email
      login_password: Login password
      login_email_hint: The email you use to login
      login_password_hint: If you are trying to link to another account
      programs: Program(s)
    emergency_contact_user_form:
      date_of_birth: "Date of birth"
    no_information: 'No information'
    action_users_menu:
      select: Settings
      action: Action
      action_standby: Standby
      action_completed: Completed
      action_discontinued: Discontinued
      action_active: Active
      action_block: Block
      action_unblock: Unblock
      action_deactivate: Deactivate
      action_activate: Activate
      confirm_standby: Are you sure you want to change the program status to standby on the selected student(s)?
      confirm_completed: Are you sure you want to change the program status to completed on the selected student(s)?
      confirm_discontinued: Are you sure you want to change the program status to discontinued on the selected student(s)?
      confirm_active: Are you sure you want to change the program status to active on the selected student(s)?
      confirm_block: Are you sure you want to block the selected user(s)?
      confirm_unblock: Are you sure you want to unblock the selected user(s)?
      confirm_deactivate: Are you sure you want to deactivate the selected user(s)?
      confirm_activate: Are you sure you want to activate the selected user(s)?
      confirm_resend_login_email: Are you sure you want to resend the login email to the selected user(s)?

    guest_form:
      access_checkins: Access
      supervisees_header: Guest user access
      add_guest_description: Below you can give this guest access to one or multiple users through a view-only mode.
      users_added: "Users added:"
      add_users: Please choose which users
      blank_option_description: None
      button_add_user: Add user/class
      settings_header: Guest access settings
      user_programs: "Access to user programs\n(including progress and report generator)"
      user_theory: Access to user theory
      user_certificates: "Access to user certificates\n(including certificate warnings)"
      user_documents: Access to user documents
      user_bookings: Access to user bookings
    events:
      ground_school_warnings: Ground training warnings
    users_box:
      confirm_deletion: |
        Are you sure you want to delete this user?
        (This cannot be undone)
      remove_student_mark: It is not possible to delete this user as this user is also a renter, instructor or administrator. Do you wish to remove the student role from this user anyway?
    availabilities:
      from: From
      title: Availabilities
      to: To
    caa_ref_num: CAA Ref. number
    calendar:
      calendar_sync_info: Copy and use the URL below when setting up your calendar synchronization.<br><br>
        You can revoke access by clicking renew which will generate a new link for calendar synchronization. The previous link will cease to work when a new link is generated.<br><br>
        Include a checkmark in "certificate expiry" to add synchronization of your certificate expiry dates.
      google_calendar: Google Calendar
      guides: Video guides
      guides_info: The video guides below will guide you through the set up of calendar synchronization with different calendars.
      google_calendar_info: Note that Google Calendar will only update feeds every 12 hours or less, hence we recommend using another calendar for your synchronization.
      icloud_calendar: Apple Calendar
      outlook_calendar: Outlook Calendar
      info: Info
      sync_with_calendar: Sync with calendar
      calendar_certificate_feed: Include certificate expiry
      renew: Renew
      renew_explaination: Renew calendar link
    report_title:
      report_for: "%{from} - %{to}"
      log_totals: "Primary/Secondary/Tertiary: %{primary_total}/%{secondary_total}/%{tertiary_total}"
    income:
      billing_overview: Income
    edit:
      title: Edit user
    edit_certificates:
      beg: BEG
      gen: GEN
      medical: Medical
      n_beg: N-BEG
      status: Status
      update: Update certificates
    edit_form:
      update: Update
      duplicate_user_references: 'Conflicts'
    roles_form:
      administrates_bookings: Booking administrator
      administrates_checkings: Can access the checkin/out
      administrates_flags: Repetition administrator
      administrates_rentals: Rental administrator
      permissions: Permissions
    unsubscribe_announcements_form:
      email_unsubscribe_note: |
        Please take note that by unsubscribing from the FlightLogger announcements, you will no longer get email notifications about improvements or new features added to FligthLogger. This is not limited to improvements that you may have specifically requested.

        Furthermore, you will no longer receive email notifications when FlightLogger is planning for downtime due to maintenance or in the rare event we are experiencing operational issues.

        Even though you will still receive all updates from us in your FlightLogger inbox, we recommend that not all users unsubscribe from our email announcements to ensure that important information is not overlooked.
    flash:
      administrator: The administrator has been created
      instructor: The instructor has been created
      flight_instructor: The flight instructor has been created
      ground_instructor: The ground instructor has been created
      renter: The renter has been created
      student: The student has been created
      guest: The guest has been created
      crew: The crew has been created
      staff: The staff has been created
    flash_activated: The user has been activated
    flash_deactivated: The user has been deactivated
    flash_maintenance_removed: The user you deactivated had a designated maintenance role, which has been removed from the user.
    flash_certificate: The certificates has been updated
    flash_requirements: The requirements has been updated
    flash_requirements_no_change: No change in requirements
    flash_timezone: The timezone is ignored for the next week
    flash_updated: The user has been updated
    flash_updated_bulk_student: "The student(s) has been set to %{action} in the selected programs"
    flash_updated_bulk_block: The user(s) has been blocked
    flash_updated_bulk_unblock: The user(s) has been unblocked
    flash_updated_bulk_mail: An email has been sent to the user(s) with instructions
    flash_updated_bulk_activate: The user(s) has been activated
    flash_updated_bulk_deactivate: The user(s) has been deactivated
    flash_updated_bulk_password: The user(s) will be forced to reset their passwords before next login
    flash_updated_bulk: The user(s) has been updated
    flash_updated_bulk_error: The user(s) failed to be updated, try again later.
    flash_updated_unauthorized: You are not authorized to do this action.
    flash_updated_neutralized: One or more users is neutralized, and restricted from the action.
    flash_updated_bulk_existing:
      active: active
      standby: standby
      mixed: active/standby
      error_message_single: |
        The student: %{student} is already %{status} on %{program_name} and cannot be made %{status}.
        In order to change the program status, the currently %{status} version must be set to completed or discontinued on the student page.
        No changes have been made.
      error_message_plural: |
        The students: %{students} are already %{status} on %{program_name} and cannot be made %{status}.
        In order to change the program status, the currently %{status} version must be set to completed or discontinued on the student page.
        No changes have been made to any of the selected students.
    flash_reset_password_email_sent: An email has been sent to the user with instructions
    flash_rental_permission_updated: Rental permissions successfully saved.
    form:
      create:
        administrator: Create administrator
        instructor: Create instructor
        flight_instructor: Create flight Instructor
        ground_instructor: Create ground Instructor
        renter: Create renter
        student: Create student
        guest: Create guest
        crew: Create crew
        staff: Create staff
    index_administrator:
      title: Administrators
    index_instructor:
      title: Instructors
    index_renter:
      title: Renters
    index_guest:
      old:
        guests: Deactivated guests
      title: Guests
    index_crew:
      old:
        crews: Deactivated crew
      title: Crew
    index_staff:
      old:
        staffs: Deactivated staff
      title: Staff
    index_student:
      title: Students
      without_program: Without program
      without_class: Students without a class
      students: "Students"
      ground_instructors: Ground instructors
      flight_instructors: Flight instructors
    new:
      title:
        administrator: New administrator
        instructor: New instructor
        flight_instructor: New flight instructor
        ground_instructor: New ground instructor
        renter: New renter
        student: New student
        guest: New guest
        crew: New crew
        staff: New staff
    old:
      button:
        guest_active: Active guests
        crew_active: Active crew
        staff_active: Active staff
        administrator_active: Active administrators
        instructor_active: Active instructors
        renter_active: Active renters
      title:
        crew: Deactivated crew
        staff: Deactivated staff
        administrator: Deactivated administrators
        instructor: Deactivated instructors
        renter: Deactivated renters
        student: Deactivated students
        guest: Deactivated guests
    overview:
      billing_overview: Income
      instructor_overview: Instructor overview
      program_overview: "%{program} overview"
    progress_tests:
      confirm: Confirm
      create: Create
      date: Date
      delete: Delete
      grade: Grade
      instructor: Instructor
      subject: Lesson
      title: Title
      update: Update
    rentals:
      new_rental: Create new rental
      title: Rentals
    programs_dropdown:
      programs: Programs
      active: Active
      standby: Standby
      completed: Completed
      discontinued: Discontinued
      add_program: Add new program
      external: External
      student_consent_missing: "Student consent missing"
    programs_dropdown_old:
      programs: Programs
      active: Active
      standby: Standby
      completed: Completed
      discontinued: Discontinued
      add_program: Add new program
      external: External
      student_consent_missing: "Student consent missing"
    user_activity_tab:
      last_login: "Last login"
      created_at: "Created at"
      user_has_not_logged_in: This user has never logged in
      first_login: "First login"
      last_action: "Last action"
      blocked: "Blocked"
      next_booking: "Next booking"
      last_duty: "Last duty"
    user_duty_time_indicators:
      on_duty: On duty
      off_duty: Off duty
      online: Online
      offline: Offline
    show:
      accounting: Account overview
      activate: Activate
      are_you_sure: Are you sure you want to deactivate the user?
      analytic_dashboard: FlightLogger Business Insights
      income_report: Income
      block_user: Block user
      booking: Booking
      bookings: Bookings
      cancelled_bookings: Cancelled bookings
      certificates: Certificates
      certificates_2: Certificates
      disable: Deactivate
      documents: Documents
      edit_user: Edit info
      user_watchable: Restrict user access
      guest_watchable: Guest user access
      extra_theory: Extra theory
      expenses_report: Expenses
      raw_theory_report: Raw data - theory report
      raw_flight_report: Raw data - flight report
      lessons: Class theory
      program_overview: "%{program} - A"
      program_total_overview: "%{program} - B"
      progress_tests: Progress tests
      releases: Releases
      rentals: Rentals
      reports: Reports
      rewoke_inactive: Reactivate user
      resend_welcome_mail: Resend the welcome email
      delete_user: Delete
      neutralize_user: GDPR neutralize
      settings: Settings
      accounting_settings: Accounting settings
      show_emergency_contect_link: Emergency
      show_user_contect_link: Normal
      theory: Theory
      theory_exams: Exams
      theory_releases: Theory releases
      type_questionnaires: Type questionnaires
      unblock_user: Remove blocking
      update_image: Edit image
      certificate_requirements: Requirements
      logbook: Logbook
      generate_report: Report generator
      cloudsync_resync: Synchronize with DBM (%{providers})
      force_password_change: Force password reset
      force_password_change_confirm: "Are you sure you want to force a password reset for %{user}?\nThe user will not be able to use FlightLogger before changing his/her password."
    user_availability_menu_item:
      availabilities: Availability
      availability_and_duty: Availability & duty time
      duty_time: Duty time
    sidebar:
      new:
        administrator: Create administrator
        instructor: Create instructor
        renter: Create renter
        student: Create
        guest: Create guest
        crew: Create crew
        staff: Create staff
      old:
        administrators: Deactivated administrators
        instructors: Deactivated instructors
        renters: Deactivated renters
        students: Deactivated students
        guests: Deactivated guests
        crews: Deactivated crew
        staffs: Deactivated staff
      total_users: "Total: "
      students_with_a_completed_program: Completed students
      students_with_a_standby_program: Standby students
      students_with_an_active_program: Active students
      students_with_a_discontinued_program: Discontinued students
      sort_by: "Sort by: %{method}"
      program: Programs
      class: Classes
      student: Students
    statistics:
      all_exercise_average: "All exercises (average %{average})"
      statistic_group_average: "%{name} (average %{average})"
      choose_exercise: Choose statistic group
      choose_program: Choose a program
      programs: Programs
      progress: Progress
      progress_tracker: Exercise tracker
      cbta_matrix: CBTA matrix
      progress_for: Progress for
    user_blocked: The user was blocked
    user_no_longer_inactive: The user is no longer inactive
    user_not_blocked: Couldn't block the user, please try again
    user_not_unblocked: Couldn't unblock the user
    user_unblocked: The user was unblocked
    on_duty: Currently on duty
    online: Currently online
    rental_permissions:
      title: "Rental permissions for %{callsign}"
      info: Choose below if the renter can request rental of all aircraft and/or simulator(s). Alternatively specify which aircraft and/or simulator(s) can be requested as a rental.
      submit: Save
      checkbox_aircraft_renter: Allow renting of all current and future rentable aircraft
      checkbox_simulator_renter: Allow renting of all current and future rentable simulators
    rentable_planes_table:
      aircraft_header: Aircraft
      simulator_header: Simulators
      aircraft_disabled_notice: The user is an aircraft renter, and can thus rent all aircraft. Remove the aircraft renter permission of the user to change individual aircraft rights.
      simulator_disabled_notice: The user is a simulator renter, and can thus rent all simulators. Remove the simulator renter permission of the user to change individual simulator rights.
      callsign: Call sign
      model: Model
      prices: Prices
      aircraft_none_found: There are no active, rentable aircraft.
      simulator_none_found: There are no active, rentable simulators.
    users_list:
      status: Status
      adresse: Address
      warnings: Warnings
      edit_user: Edit
      email: Email
      name: Name
      tlf: Phone
      country: Country
      caa_ref_num: CAA Ref.
      reference: User Ref.
      zipcode: Post code
      city: City
      roles: Roles
      block_status: status
    search:
      search_neutralized: Show all GDPR neutralized
      search: Search
      noresult: 'Sorry, no matches found for <b>"%{term}"</b>'
      help:
        title: 'Search Suggestions:'
        suggestions:
          - Check your spelling
          - Try more simple words
      deactivated: Deactivated
    generate_report:
      header: Report generator
      report_format: Format
      user_flight_report: Flight training
      programs: Program
      include: Include
      flight_time: Flight time
      student_folder: Program folder
      theory_report: Ground training
      course: Course
      extra_theory: Extra theory
      type_questionnaires: Type questionnaires
      generate: Generate
      course_disabled_true: Active
      course_disabled_false: Deactivated
      please_select_one: Please select at least one
      competencies: Include competencies
      base_theory: Class theory, Progress tests, Theory releases, Exams
      chief_instructor: Chief Instructor
      missing_chief_instructor: Chief Instructor must be specified
      missing_flight_programs: User has not been registered to any flight training program
    sync_to_provider:
      success: "The user is now being synchronized to %{provider}"
    filter_modal:
      group_by: Group by
      active_non_active: Active/Deactivated
      program_assignment_filter: Date range (program enrollment)
      reset: Reset
      search: Search
      include_deactivated: Include deactivated
    expenses:
      include_invoiced: Include invoiced
    sidebar_partials:
      sidebar_default:
        deactivated: Deactivated
        active: Active
        create: Create
      sidebar_instructor:
        flight_instructor: Flight instructor
        ground_instructor: Ground instructor
        instructor: Instructor
        create: Create
        deactivated: Deactivated
        active: Active
        instructors_role_dropdown:
          all: All
          flight_instructor: Flight
          ground_instructor: Ground
      sidebar_student:
        class: Class
        student: Student
        create: Create
        students_state_dropdown:
          completed: Completed
          standby: Standby
          active: Active
          discontinued: Discontinued
          no_program: Without program
        program_state_dropdown:
          all: All
          helicopter: Helicopter
          airplane: Airplane
          ground: Ground
    info_box_this_user_already_has_an_active_account:
      this_user_already_has_an_active_my_flight_logger_account: "This user already has an active my|FlightLogger account and will be invited to your organization upon creation!"
    info_box_with_anchor_to_collaboration:
      this_student_already_has_an_active_my_flight_logger_account: "This student already has an active my|FlightLogger account and will be invited to your organization upon creation!"
      you_can_get_access_to_all_the_students_training_programs_by_collaborating_with_other_flight_logger_organizations: "You can get access to all the students training programs by collaborating with other FlightLogger organizations."
      to_initiate_a_collaboration_simply_go_to: "To initiate a collaboration simply go to "
      administration_->_collaborations: "Administration -> Collaborations"
    instructor_types:
      instructor: All instructors
      flight_instructor: Flight instructors
      deactivated_flight_instructor: Deactivated Flight instructors
      ground_instructor: Ground instructors
      deactivated_ground_instructor: Deactivated Ground instructors
    navbar:
      active: Active
      standby: Standby
      completed: Completed
      discontinued: Discontinued
      availabilities: Availability
      availability_and_duty: Availability & duty time
      duty_time: Duty time
      accounting: Account overview
      activate: Activate
      are_you_sure: Are you sure you want to deactivate the user?
      analytic_dashboard: FlightLogger Business Insights
      income_report: Income
      block_user: Block user
      booking: Booking
      bookings: Bookings
      cancelled_bookings: Cancelled bookings
      certificates: Certificates
      disable: Deactivate
      documents: Documents
      edit_user: Edit info
      user_watchable: Restrict user access
      guest_watchable: Guest user access
      extra_theory: Extra theory
      expenses_report: Expenses
      raw_theory_report: Raw data - theory report
      raw_flight_report: Raw data - flight report
      lessons: Class theory
      program_overview: "%{program} - A"
      program_total_overview: "%{program} - B"
      progress_tests: Progress tests
      releases: Releases
      rentals: Rentals
      reports: Reports
      permissions: Permissions
      revoke_inactive: Reactivate user
      resend_welcome_mail: Resend the welcome email
      delete_user: Delete
      neutralize_user: GDPR neutralize
      settings: Settings
      accounting_settings: Accounting settings
      show_emergency_contact_link: Emergency
      show_user_contact_link: Normal
      theory: Theory
      theory_exams: Exams
      theory_releases: Theory releases
      type_questionnaires: Type questionnaires
      unblock_user: Remove blocking
      update_image: Edit image
      certificate_requirements: Requirements
      logbook: Logbook
      generate_report: Report generator
      cloudsync_resync: Synchronize with DBM (%{providers})
      force_password_change: Force password reset
      force_password_change_confirm: "Are you sure you want to force a password reset for %{user}?\nThe user will not be able to use FlightLogger before changing his/her password."
      programs: Programs
      add_program: Add new program
      external: External
      edit_user_program: Change status of the program
      user_program_changelog: Changelog of the program
      student_consent_missing: "Student consent missing"
      user_accountings:
        title: Accounting
        cards: Payment methods
        invoices: Invoices
        balances: Balance
        settings: Settings
        payments: Payments
    student_programs_list:
      name: "Name"
      revision: "Revision"
      enrollment: "Enrolled"
      last_activity: "Last activity"
      phase_subject: "Phase/Subject"
      lesson: "Lesson"
      progress: "Progress"
      complete: "completed"
      hours: "hours"
  users_teams:
    flash_created: The student(s) have been added to the class
    flash_error: Student(s) could not be added to the class
    flash_destroyed: The student has been removed from the class
    new:
      add_students_to_team: Add students to class - %{team}
      students: Students
      add_student: Add students
  email_templates:
    reset_to_default: Email has been reset
    saved_correctly: The email was saved correctly.
    form:
      reset_to_default: Reset to default
  account_theory_report:
    table_header: Theory
    no_theory: "There is no theory yet"
    column_headers:
      account: Account
      date: Date
      classroom: Classroom
      type_of_theory: Type
      type: Type
      program: Program
      subject: Subject
      lesson: Lesson
      lesson_note: Lesson/Note
      description: Description
      ref_number: Ref. number
      sitting_number: Sitting number
      sitting_starts_at: Sitting starts at
      sitting_ends_at: Sitting ends at
      instructor: Instructor
      instructor_caa_ref_num: Instructor CAA ref.
      instructor_reference: Instructor user ref.
      examiner: Examiner
      examiner_caa_ref_num: Examiner CAA ref.
      examiner_reference: Examiner user ref.
      class_name: Class
      student: Student
      student_caa_ref_num: Student CAA ref.
      student_reference: Student user ref.
      starts_at: Starts at
      ends_at: Ends at
      duration: Duration
      status: Status
      attended_from: Attended from
      attended_to: Attended to
      attendance: Attendance
      grade: Grade
      student_comment: Student comment
      comment: Comment
      booking_id: Booking id
      income_invoice_number: Income invoice number
      expenses_invoice_number: Expenses invoice number
      instructor_id: Instructor id
      examiner_id: Examiner id
      student_id: Student id
      theory_id: Theory id
      team_theory_id: Team theory id
      updated_at: Updated at
      created_at: Created at
      user_program_id: User program id
      program_revision_id: Program revision id
      program_id: Program id
  duty_report:
    report_for: Duty report for %{from} - %{to}
    id: Id
    account: Account
    date: Date
    type_of_activity: Type of activity
    user: User
    user_id: User ID
    user_caa_ref: User CAA ref.
    user_ref: User ref.
    starts_at: Starts at
    ends_at: Ends at
    duration: Duration
    comment: Comment
    expenses_invoice_number: Expenses invoice number
    updated_at: Updated at
    created_at: Created at
    no_records: "There are no duty times yet"
  flight_report:
    account: Account
    date: Date
    flight_id: Flight ID
    aircraft: Aircraft
    aircraft_type: Aircraft type
    type_of_activity: Type of activity
    kind_of_operation: Kind of Operation
    program_name: Program name
    program_revision: Program revision
    pics: PIC
    pics_caa_ref_num: PIC CAA ref.
    pics_reference: PIC user ref.
    instructors: Instructor
    instructors_caa_ref_num: Instructor CAA ref.
    instructors_reference: Instructor user ref.
    students: Student
    students_caa_ref_num: Student CAA ref.
    students_reference: Student user ref.
    renters: Renter
    departure_fuel: Departure fuel
    fuel_added: Fuel added
    renters_caa_ref_num: Renter CAA ref.
    renters_reference: Renter user ref.
    crew: Crew
    crew_caa_ref_num: Crew CAA ref.
    crew_reference: Crew user ref.
    customer: Customer
    departure: Departure
    arrival: Arrival
    off_block: Off block
    takeoff: Takeoff
    landing: Landing
    on_block: On block
    timer_start: Timer start
    timer_finish: Timer finish
    block_time: Block time
    airborne_time: Airborne time
    timer: Timer
    pilot_flying_time: Pilot flying
    pilot_monitoring_time: Pilot monitoring
    fuel_coefficient: Fuel coefficient
    calculated_fuel: Calculated fuel
    vfr_time: VFR
    ifr_time: IFR
    flight_type: Flight type
    day_time: DAY
    night_time: NIGHT
    local_time: Local
    cross_country_time: Cross country
    instrument_time: IF time
    asymmetric_time: Asymmetric time
    landings: Full stop
    touch_and_go: Touch and go
    approaches: Approach
    go_around: Go around
    comment: Comment
    booking_ref: Booking id
    aircraft_id: Aircraft id
    income_invoice_number: Income invoice number
    expenses_invoice_number: Expenses invoice number
    lesson_status: Lesson status
    lesson_status_passed: Passed
    lesson_status_failed: Repetition lesson needed
    lesson_status_not_flown: Not flown
    lesson_id: Lesson id
    cost: Cost
    no_flights: "There are no flights yet"
    instructor_id: Instructor id
    user_id: User id
    customer_id: Customer id
    briefing_time: Briefing time
    debriefing_time: Debriefing time
    updated_at: Updated at
    created_at: Created at
    program_phase: Program phase
    user_program_id: User Program id
    total_program_time: Total program time
  logbook_report:
    title: "%{first_name} %{last_name}'s logbook"
    remarks_and_endorsements: "Remarks and endorsements"
    landings_night: "Landings night"
    landings_day: "Landings day"
    synthetic_training: "Synthetic training"
    dual: "Dual"
    flight_instructor: "Flight instructor"
    multi_pilot: "Multi-pilot"
    co_pilot: "Co-pilot"
    pic: "Pilot in command"
    multi_ifr: "Multi engine ifr"
    multi_vfr: "Multi engine vfr"
    single_ifr: "Single engine ifr"
    single_vfr: "Single engine vfr"
    night: "Night"
    total: "Total"
    name_of_pic: "Name of pilot in command"
    registration: "Registration"
    type_of_aircraft: "Type of aircraft"
    on_block: "On block"
    arrival: "Arrival"
    off_block: "Off  block"
    departure: "Departure"
    date: "Date"
    if: "IF"
    instructor_synthetic_training: "Instructor synthetic training"
  api:
    authentication:
      error: "Login failed"
  kiosk_user_operation:
    email_already_used: is already in use
    password_not_confirmed: doesn't match password
    missing_user: Couldn't find user
    missing_kiosk_user: Account doesn't have a kiosk user
  instructor_abbreviated: 'Inst.'
  no_lectures: "There is no theory yet"
  conversation_creation_form:
    is_blank: "is blank"
    is_zero: "is zero"
    is_missing: "is missing"
  account_invoices:
    index:
      pdf: "PDF"
      invoice_amount: "Invoice amount"
      payment_date: "Due date"
      charge_status: "Charge status"
      no_outstanding_invoices: "No outstanding invoices"
      title: "Invoice history"
      invoice_no: "Invoice no."
  custom_report:
    theory_releases: Theory Releases
    progress_tests: Progress Tests
    theory_exams: Exams
    theory_lectures: Class Theory
    theory_report: Theory Report
    ptr_certificate: PTR Certificate
    standard:
      title: Standard
    certificate_common:
      certified: Certified
      user_caa_ref_no: User Reference No.
      account_caa_ref_no_text: – Certificate No.
      enrolled_as_of: Enrolled as of
      software_managed_by_1: Certified and digitally signed by FlightLogger’s
      software_managed_by_2: flight training management software.
    ptr_certificate_cover:
      pilot_training_record: Pilot Training Record
      transport_canada_file_no: Transport Canada File No.
      verified_and_digitally_signed_by: Verified and digitally signed by
      flightloggers_flight_training: FlightLogger’s flight training
      management_software: management software.
      last_flight: Last flight
    enrollment_certificate:
      title: Enrollment Certificate
      certified_text_1: This is to certify that
      certified_text_2: is enrolled in the
      certified_text_3: Federal Aviation Administration approved
      certified_text_4: conducted by
    graduation_certificate:
      title: Graduation Certificate
      graduated_text_1: This is to certify that
      graduated_text_2: has satisfactorily completed each
      graduated_text_3: required stage of the Federal Aviation Administration approved
      graduated_text_4: including the tests for those stages.
      graduated_as_of: Graduated as of
      chief_instructor: Chief Instructor
      cross_country_training: hours of cross-country training
    ptr_certificate_file:
      program: "Program:"
      student: "Student:"
      enrolled: "Enrolled:"
      sim: "SIM"
      single_engine: "SINGLE ENGINE"
      multi_engine: "MULTI ENGINE"
      cross_country: "CROSS COUNTRY"
      instrument: "INSTRUMENT"
      day: "Day"
      night: "Night"
      date: "DATE"
      aircraft: "AIRCRAFT"
      reg: "REG"
      lesson_plan: "LESSON PLAN"
      instructor: "INSTRUCTOR"
      student_upper_case: "STUDENT"
      pic: "PIC"
      dual: "DUAL"
      solo: "SOLO"
      hood: "HOOD (IF)"
      ftd: "FTD (IFR SIM)"
      act: "ACT(IFRDUAL)"
      apr: "#APR"
      route_of_flight: "ROUTE OF FLIGHT"
      tc_file_no: "- TC FILE NO."
      n_a: "N/A"
      total:
        all_entries_are_certified: "All entries are certified true and correct"
        totals: "TOTALS"
        student_upper_case: "STUDENT"
        signature: "Signature"
        date: "Date"
        instructor_cfi: "Instructor CFI"
    ptr_lesson_grading:
      date: Date
      lesson_plan: Lesson Plan
      familiarization: Familiarization
      preparation_for_flight: Preparation for Flight
      ancillary_controls: Ancillary Controls
      taxiing: Taxiing
      attitude_and_movements: Attitudes and Movements
      straight_and_level_flight: Straight and Level Flight
      climbing: Climbing
      descending: Descending
      turns: Turn/Steep Turn
      helicopter_turns: Turns
      maximum_range_and_endurance: Maximum Range and Endurance
      slow_flight: Slow Flight
      stalls: Stall
      spinning: Spin
      spiral_dives: Spiral
      slide_slipping: Sideslip
      take_off: Take-Off
      take_off_normal: Normal
      take_off_short_field: Short / Minimum Run
      take_off_soft_field: Soft / Rough
      take_off_obstacle: Obstacle
      take_off_crosswind: Crosswind
      circuit_operations: Circuit
      approach_and_landing: Approach and Landing
      approach_landing_normal: Normal
      approach_landing_short_field: Short
      approach_landing_soft_field: Soft / Rough
      approach_landing_obstacle: Obstacle
      approach_landing_crosswind: Crosswind
      approach_landing_precision: Precision 180
      first_solo: First Solo
      illusions_created_by_drift: Illusions Created by Drift
      precautionary_landings: Precautionary Landing
      forced_landings: Forced Landing
      helicopter_forced_landings: Forced Approaches
      pilot_navigation: Pilot Navigation
      helicopter_pilot_navigation: Navigation
      pilot_navigation_departure_procedures: Departure Procedures
      pilot_navigation_enroute_procedures: Enroute Procedures
      pilot_navigation_diversion: Diversions
      instrument_flight: Instrument Flying
      instrument_flight_full_panel: Full Panel
      instrument_flight_limited_panel: Limited Panel
      instrument_flight_unusual_attitude_recovery: Unusual Attitudes
      instrument_flight_radio_navigation: Radio Navigation
      helicopter_instrument_flight_radio_navigation: Radio Aids
      emergency_procedures: Emergency Procedures
      emergencies: Emergencies
      tail_rotor_failure: Tail Rotor Failure
      aircraft_systems_failure: Aircraft Systems
      radio_communications: Radio Communication
      upper_air: Upper Air
      effect_of_controls: Effects of Controls
      air_speed_power_changes_straight_level_flight: "Airspeed & Power -\n Straight & Level"
      climbs_and_descents: Climbs & Descents
      autorotation_one: Autorotations 1
      hovering: Hovering
      take_off_and_landing: Take-Off and Landing
      hover_flight: Hover Flight
      hovering_exercises_taxi: Exercises - Taxi
      hovering_exercises_turns: Exercises - Turns
      engine_failure_at_hover: Engine Failure
      transitions: Transitions
      autorotation_two: Autorotations 2
      side_rear_flight: Sideways and Rearwards Flight
      steep_turns: Steep Turns
      autorotation_three: Autorotations 3
      rapid_decelerations: Rapid Decelerations
      low_level_operations: Low Flying
      sloping_ground: Sloping Ground
      advanced_take_off_and_landing: Adv. Take-Offs & Landings
      confined_areas: Confined Areas
      vortex_ring: Vortex Ring
      practical_loading_max_weight_operations: Max. Weight Operations
      sling_load_operations: Slinging
      type_conversion: Type Conversion
    user_flight_report: Program Report
    other_theory: Other Theory
    program_folder:
      lessons: "Lessons:"
  payment_report:
    receipt_common:
      payment_receipt: Payment Receipt
      payment_failed: Payment Failed
      refund_receipt: Refund Receipt
      payment_line1: "Payment method: %{method}"
      payment_line1_with_last4: "Payment method: %{method} (#### #### #### %{last4})"
      payment_line2: "Transaction Id: %{id}"
      total: Total
      tax: Tax
      sub_total: Sub Total
    qim:
      invoice_no: "Invoice #%{no}"
    ubm:
      transaction_no: "Transaction No. %{no}"
  booking_choosers:
    no_bookings: "There are no open bookings to choose"
    cant_proceed_completed_booking: The booking has already been completed
    cant_proceed_cancelled_booking: The booking has already been cancelled
    user_not_on_booking: The booking is no longer associated with the user
    booking_type_mismatch: The booking is not of the right type for this kind of registration
  en:
    automatic_export_credentials:
      provider_now_disabled: "Backup integration with the provider is now disabled"
      something_went_wrong: "Something went wrong"
  flight_tracker_credentials:
    form:
      title: AirNav RadarBox
      save: Save
      show_flight_tracking_area: Show "Flight tracking" area during flight registration.
      confirm_destroy: Are you sure you want to stop the integration with AirNav RadarBox?
      destroy: Stop integration
      airnav_radarbox_info: The integration with AirNav Systems enables live tracking of your fleet while airborne as well as the ability to retrieve and store flight tracks within FlightLogger. No subscription is necessary, as the option to collect flight tracks from AirNav Systems is included for free and preconfigured with your FlightLogger subscription. Learn more by clicking the info icon.
      spidertracks_info: The integration with Spidertracks enables live tracking of your fleet while airborne as well as the ability to retrieve and store flight tracks within FlightLogger. To get started you need a subscription as well as tracking devices (Spiders) in your aircraft. Learn more by clicking the info icon.
      other_providers_info: Using other flight track providers will only enable live tracking of your fleet while airborne.
      flight_tracker_settings:
        tracker_provider: Choose your preferred flight tracking provider to enable live flight tracking from the Booking and the Departure/Arrival pages in FlightLogger
        update_existing_tracker_providers: Enforce new flight tracker URL on all aircraft
        save: Save
  cloudsync_mailer:
    grant_error:
      subject: "FlightLogger - %{provider} lost access, action required!"
    notify_not_syncing:
      subject: "FlightLogger - %{provider} is not synchronizing"
    notify_almost_full:
      subject: "FlightLogger - %{provider} is almost full"
    notify_insufficient_permissions:
      subject: "FlightLogger - %{provider} is not synchronizing"
  users_mailer:
    new_member_password:
      subject: New FlightLogger login system
      dear_user: "Dear %{name}"
      new_login_system_explanation: "At FlightLogger we recently decided to update our login system to enable users to login to multiple flight schools with a single login. We noticed that you currently use the email '%{email}' to login to the following schools:"
      please_choose_new_password: Now that we have combined your logins for these schools, we have to request that you select a new password for your new combined login. The password can be anything you like including previously used passwords.
      click_link: "Please click the following link to select a new password:"
      best_regards: Best regards
      flightlogger: FlightLogger
  previous_experiences:
    edit:
      go_back: "Go back to user program"
      save: "Save credited hours"
  team_type_questionnaires_report:
    report:
      confirm: Are you sure you want to delete this type questionnaire?
      date: Date
      title: Type questionnaires
      type: Type
      no_questionnaires: There are no questionnaires yet
      list_empty: "There are no type questionnaires yet"
      confirm_delete: Are you sure you want to delete this type questionnaire?
  theory_exams:
    ref_num: "Ref. number"
  exam_report:
    title: "Exam report on %{date}"
  progress_test_report:
    title: "Progress test report on %{date}"
  theory_release_report:
    title: "Theory release report on %{date}"
  class_theory_lecture_report:
    title: "Class theory report on %{date}"
  rep_warning: "You have not flagged any exercises. Are you sure you want to save and create a repetition lesson?"
  pass_warning: "You have flagged some exercises. Are you sure you want to save without creating a repetition lesson?"
  checkin_acknowledgement:
    student_default_text: '<b>STUDENT ACKNOWLEDGEMENT<br><br></b><ul><li>Flight will be conducted in accordance with the Flight Training Operations Manual.</li><li>Flight will be conducted in accordance with the Aviation Regulations.</li><li>All weather and NOTAMS have been thoroughly reviewed for safety.</li><li>Student has received a pre-flight briefing from a qualified flight instructor.</li><li>Student has received all necessary preparatory ground instruction from a qualified flight instructor.</li><li>All pilot documents have been verified and are valid.</li><li>All pilots accept the TERMS OF USE policy.</li></ul>'
    instructor_default_text: '<b>INSTRUCTOR AUTHORIZATION<br><br></b><ul><li>Flight will be conducted in accordance with the Flight Training Operations Manual.</li><li>Flight will be conducted in accordance with the Aviation Regulations.</li><li>All weather and NOTAMS have been thoroughly reviewed for safety.</li><li>Student has received a pre-flight briefing from a qualified flight instructor.</li><li>Student has received all necessary preparatory ground instruction from a qualified flight instructor.</li><li>All pilot documents have been verified and are valid.</li><li>All pilots accept the TERMS OF USE policy.</li></ul>'
    crew_default_text: '<b>CREW AUTHORIZATION<br><br></b><ul><li>Flight will be conducted in accordance with the Flight Training Operations Manual.</li><li>Flight will be conducted in accordance with the Aviation Regulations.</li><li>All weather and NOTAMS have been thoroughly reviewed for safety.</li><li>All pilot documents have been verified and are valid.</li><li>All pilots accept the TERMS OF USE policy.</li></ul>'
    renter_default_text: '<b>RENTER ACKNOWLEDGEMENT<br><br></b><ul><li>Flight will be conducted in accordance with the Flight Training Operations Manual.</li><li>Flight will be conducted in accordance with the Aviation Regulations.</li><li>All weather and NOTAMS have been thoroughly reviewed for safety.</li><li>All pilot documents have been verified and are valid.</li><li>All pilots accept the TERMS OF USE policy.</li></ul>'
  calendar_feed:
    certificate:
      document: Document(s)
      expiry: expiry
  partials:
    flight_contacts:
      contact_feedback: Feedback
      contact_maintenance: Maintenance
      contact_sms: Safety management
  frontend:
    profile:
      save_password: Update
      title: Password Reset
      sub_title: Enter a new password and confirm it below
      password: Password
      confirm_password: Password Confirmation
      new_password: Create Password
      create_password: Create Password
      pitch_header: 'With my|FlightLogger You Get:'
      pitch_access: Single sign-on to all your FlightLogger organizations
      pitch_logbook: Electronic logbook gathering all flights in one system
      organizations: 'Your Current FlightLogger Organizations:'
      organizations_alt: 'Your Current FlightLogger Organizations:'
    invitation:
      invitation_client: 'Organization Inviting You:'
    login:
      forgot_password: 'Forgot Password?'
      title: 'Sign In'
      email: 'Email Address'
      password: 'Password'
      submit: 'Sign In'
      session_security:
        label: Security
        public: 'This is a public or shared device'
        private: 'This is a private device'
      invalid: 'Wrong email or password. Please try again.'
      sub-title: 'Welcome to'
      top-title: 'my|FlightLogger'
      footer:
        copyright: "© Copyright 2011 – %{year} FlightLogger.net \n All Rights Reserved"
  frontpage_booking_times:
    form:
      specify_single_student_expiry: Single student bookings are only visible for
      specify_multi_student_expiry: Multi student bookings are only visible for
      specify_rental_expiry: Rental bookings are only visible for
      specify_operation_expiry: Operation bookings are only visible for
      specify_class_theory_expiry: Class theory bookings are only visible for
      specify_progress_test_expiry: Progress test bookings are only visible for
      specify_theory_release_expiry: Theory release bookings are only visible for
      specify_theory_exam_expiry: Theory exam bookings are only visible for
      specify_extra_theory_expiry: Extra theory bookings are only visible for
      specify_type_questionnaire_expiry: Type questionnaire bookings are only visible for
      enable: "Save"
      hours: "hours"
  authenticate_invoice:
    show:
      payment_requres_authentication: Payment requires authentication
      amount: "Amount: %{amount}"
      invoice_number: "Invoice number: %{number}"
      press_authorize: Please press "Authorize payment" to retry this payment.
      no_action: No action is needed for this payment.
      pdf: Invoice PDF
      successful: Your payment was authorized. Have a nice day!
      invoice_history: Invoice history
  flight_phases:
    flight_phase:
      confirm_disable_flight_phase: "Are you sure, that you would like to deactivate flight phase: %{flight_phase_name}?"
    index:
      title: "Flight Phases"
      active: Active
      deactivated: Deactivated
      name: Name
      abbreviation: Abbreviation
      no_activated: There are no active flight phases to be shown at the moment
      no_deactivated: There are no deactivated flight phases to be shown at the moment
  conversations:
    active_all: All active users
    active_administrators: Active administrators
    deactivated_administrators: Deactivated administrators
    flight_instructors: Active flight instructors
    deactivated_flight_instructors: Deactivated flight instructors
    ground_instructors: Active ground instructors
    deactivated_ground_instructors: Deactivated ground instructors
    active_instructors: Active instructors
    deactivated_instructors: Deactivated instructors
    active_renters: Active renters
    deactivated_renters: Deactivated renters
    active_crew: Active crew
    deactivated_crew: Deactivated crew
    active_staff: Active staff
    deactivated_staff: Deactivated staff
    active_guest: Active guest
    deactivated_guest: Deactivated guest
    active_students: Active students
    completed_students: Completed students
    standby_students: Standby students
    __students__without_program: Students without program
    discontinued_students: Discontinued students
    invalid: "This scope %{name} is not valid"
  user_watchable_resources:
    filters:
      active_students: Active students
      active_students_description: "Allow access to active students in %{watching}"
      active_or_completed_student: Active and completed students
      active_or_completed_student_description: "Allow access to active and completed students in %{watching}"
      all_students: All students
      all_students_description: "Allow access to all students in %{watching}"
  cloudsync:
    main_folder:
      booking_report: BookingStatisticsReport
      cancelled_booking_report: CancellationReport
      flight_report: RawDataFlightReport
      account_theory_report: RawDataTheoryReport
    sub_folder:
      instructors: InstructorCrewAndRenter
      programs: Business
      aircrafts: Aircraft
      expenses: Expenses
      income: Income
  user_logbook_entries:
    entry:
      date_format: "date in %{date_format} format"
      time_format: hours/minutes (00:00-23:59) in HH:MM format
      duration_format: "hours/minutes (00:00-23:59) in HH:MM format, or (0.00-999.99) in decimal format"
      confirm: "Confirm: Are you sure you want to delete this logbook entry"
  cookies_disabled: 'Please enable cookies to use FlightLogger'
  cookies_disabled_help: 'Help'
  activity_types:
    training: Training
    operation: Operation
    rental: Rental
    class_theory: Class theory
    progress_test: Progress test
    theory_release: Theory release
    exam: Exam
    type_questionnaire: Type questionnaire
    extra_theory: Extra theory
    user_duty_time: Duty time
  activities_report:
    title: "%{activity_type} activities report for %{from} - %{to}"
    title_current_only: "%{first_name} %{last_name}'s %{activity_type} activities report for %{from} - %{to}"
    columns:
      date: Date
      aircraft: Aircraft
      user: User
      instructor: Instructor
      student: Student
      departure: Departure
      arrival: Arrival
      tacho: Timer
      primary: Primary
      secondary: Secondary
      tertiary: Tertiary
      pic: PIC
      crew: Crew
      customer: Customer
      operation: Operation
      renter: Renter
      classroom: Classroom
      class: Class
      program: Program
      subject: Subject
      lesson: Lesson
      note: Note
      duration: Duration
      comment: Comment
      description: Description
      approved_by_student: Approved status
  sms:
    riskassesments:
      field:
        risk_assessment: Risk assessment
        risk_assessment_before_mitigation: Risk assessment before mitigation
  tracked_aircraft:
    connect: Connect
    connecting: Connecting...
    radarbox:
      registration: AirNav RadarBox registration
      info_general: Insert the AirNav RadarBox registration to enable AirNav RadarBox flight tracking on this aircraft.
      info_loading: Your action is being processed, this might take a while. Please check back in 10 minutes. You are free to navigate to other pages in the meantime.
      info_invalid_registration: The aircraft registration does not match any aircraft known by your tracking provider. Please ensure the AirNav RadarBox registration of the aircraft is correct.
    spidertracks:
      registration: Spidertracks registration
      info_general: Insert the Spidertracks registration to enable Spidertracks flight tracking on this aircraft.
      info_loading: Your action is being processed, this might take a while. Please check back in 10 minutes. You are free to navigate to other pages in the meantime.
      info_invalid_registration: The aircraft registration does not match any aircraft known by your tracking provider. Please ensure the AirNav RadarBox registration of the aircraft is correct.
    show:
      not_registered_info: This aircraft has not been setup with any flight track provider.
      not_available_info: The active flight track provider for this aircraft does not give the ability to retrieve and store flight tracks within FlightLogger.
      not_connected: Aircraft not connected to any flight tracking provider
      listed_attached_tracks: Listed below are attached flight tracks from previously used provider(s).
      never_integrated: Flight tracks not available
      not_available: New flight tracks not available
  pdfs:
    user_lecture_fragment:
      headers:
        grade: Grade
        norm_grade: Norm
        comment: Comment
      no_category_name: EXERCISES
      todo_item: HIL list
  unsupported_browsers:
    index:
      header: 'FlightLogger browser support'
      explanation: 'To get the best FlightLogger experience, please use one of the recommended browsers:'
      supported_version: "version %{version} and later"
  two_factor_authentication:
    index:
      activate: "Activate 2FA"
      deactivate: "Deactivate 2FA"
      submit: "Verify"
      resend: "Resend code"
      header: "Two-factor Authentication Required"
      details: "Enter the verification code that we just sent to your email %{email} (check your spam folder)"
      mail_sent: "Mail sent, check your inbox."
      what_is_this: "What is this?"
      verification_code: "Verification code"
      logout: "Cancel"
      regret: "Regret"
      send: "Send code via email"
      details_app: "Use the authenticator app to find the two-factor authentication code"
  requirements:
    add: Add requirement
    than: than
    within: within
    triggers: triggers
    duty_hours_within: duty hours within
    no_certificate_requirement: No certificate requirements yet
    no_duty_time_requirement: No duty time requirements yet
    duty_time_not_active: The duty time is not activated for this user role under account settings
    duty_time_not_active_for_user: The duty time is not activated for any of this user roles under account settings
    no_flight_time_requirement: No flight time requirements yet
    no_attempt_warning: No attempt warning yet
    no_expiry_warning: No expiry warning yet
    no_sitting_warning: No sitting warning yet
    student_configuration:
      index:
        update: "Update student requirements"
    administrator_configuration:
      index:
        update: "Update administrator requirements"
    flight_instructor_configuration:
      index:
        update: "Update flight instructor requirements"
    ground_instructor_configuration:
      index:
        update: "Update ground instructor requirements"
    staff_configuration:
      index:
        update: "Update staff requirements"
    crew_configuration:
      index:
        update: "Update crew requirements"
    renter_configuration:
      index:
        update: "Update renter requirements"
    aircraft_configuration:
      single_engine: Single engine
      multi_engine: Multi engine
      simulator: Simulator
    user_configuration:
      index:
        update: Update user requirements
    certificate_requirements_form:
      class: Class
    subtype:
      medical: Medical
      radio: Language proficiency
      general: Standard
  public_api:
    errors:
      unauthorized: "You are not authorized."
      rate_limit_reached: "You are sending too many requests to the API. Please slow down. Use the `Retry-After` header to determine when to request again."
      token_missing: "Please provide a valid API token using the 'Authorization' header."
      user_blocked: "The user of this token is currently blocked."
      token_invalid: "The provided API token is invalid."
      token_expired: "The provided API token has expired."
      account_shutdown: "The FlightLogger account has been shut down and is no longer active. Please contact administration."
      account_shutdown_imminent: "The FlightLogger account is marked for imminent shutdown. Please contact administration."
  kiosk:
    index:
      header: "Kiosk"
      already_logged_in: "Already have an active kiosk connection"
      use_tv: "Please use a tv or other device for the kiosk solution!"
      kiosk_code_explanation: "Use this code in account settings to be redirected"
  member_api_keys:
    create:
      created: "API key was successfully created!"
    destroy:
      destroyed: "API key was successfully deleted!"
    form:
      account: "Organization"
      my_flightlogger_key: "Global Key (Only for extracting data from my|FlightLogger)"
    presenters:
      my_flightlogger: "my|FlightLogger"
  wall_posts:
    posts:
      confirm_delete_wall_post: "Please note that you are about to delete a wall post for all users.\n\nAre you sure you want to delete the wall post?\nThis can not be undone!"
      create_new_message: Create
      messages: Wall posts
      messages_empty: There are no wall posts yet
      pin: Pin wall post
      unpin: Unpin wall post
      edit: Edit wall post
      delete: Delete wall post
  quickbooks_invoices:
    flightlogger_user: "FlightLogger User"
    flightlogger_resource: "Kind of Operation"
    create_drafts:
      created_at: "Registered at"
    show_drafts:
      created_at: "Drafted at"
    invoices:
      created_at: "Invoiced at"
    paid_invoices:
      created_at: "Invoiced at"
    refunded_invoices:
      created_at: "Refunded at"
      no_entries: "There are no entries in the given time period."
    receipt_pdf:
      download: Download refund receipt
    sub_total: "Subtotal"
    nett_total: "Total"
    status: "Status"
    auth_error: 'QuickBooks authorization error. Please reconnect your account.'
    integration_guard:
      no_integration: "QuickBooks is not setup."
      no_module: "QuickBooks module is not activated."
    form:
      form:
        create: "Create Draft"
        save: "Save Draft"
        quickbooks_invoice_link: "QuickBooks invoice"
        qb_customer: "Customer"
        flightlogger_item: "FlightLogger Item"
        quickbooks_item: "QuickBooks Item"
        qty: "QTY"
        price: "Price"
        price_with_symbol: "Price (%{currency_symbol})"
        amount: "Amount"
        amount_with_symbol: "Amount (%{currency_symbol})"
      submit_post:
        post: "Send to QuickBooks"
        change: "Edit Draft"
      customer_form:
        qb_customer: "QuickBooks Customer"
        qb_customer_email: "Email"
        fl_user: "FlightLogger User"
        kind_of_operation: "Kind of Operation"
        invoice_number: "Invoice #"
        txn_id: "TXN ID"
      totals_form:
        sub_total: "Subtotal"
        sub_total_with_symbol: "Subtotal (%{currency_symbol})"
        actions: "Actions"
        tax: "Tax"
        tax_with_symbol: "Tax (%{currency_symbol})"
        total: "Total"
        total_with_symbol: "Total (%{currency_symbol})"
      item_form:
        quickbooks_item: "QuickBooks Item"
        qty: "QTY"
        price: "Price"
        price_with_symbol: "Price (%{currency_symbol})"
        amount: "Amount"
        amount_with_symbol: "Amount (%{currency_symbol})"
        actions: "Actions"
      unrecognized_items_form:
        unrecognized_title: "%{items_count} unrecognized line items detected from QuickBooks"
        amount: "Amount"
        amount_with_symbol: "Amount (%{currency_symbol})"
        actions: "Actions"
    show:
      quickbooks_invoice_link: "Invoice Link"
      draft_invoice: "Draft QuickBooks Invoice"
      open_invoice: "Invoiced QuickBooks Invoice"
    overview: "Back to overview"
    tab_new_invoices: "Create Draft"
    tab_draft_invoices: "Show Draft"
    tab_invoiced_invoices: "Invoiced"
    tab_paid_invoices: "Paid"
    edit:
      edit_headline: "Draft QuickBooks Invoice"
    search_invoices: "Search"
    invoice_table:
      no_entries: "There are no entries in the given time period."
    invoice_table_total_footer:
      total: "Total"
    create_draft_table:
      no_entries: "There are no entries in the given time period."
    new:
      create_headline: "Create QuickBooks Invoice"
      draft_already_exist: "This QuickBooks invoice has already been created"
    qb_customer: "QuickBooks Customer"
    create:
      successfully_created: "QuickBooks invoice has been created."
      already_exists: "Unable to create draft as this operation is already drafted!"
    update:
      successfully_updated: "QuickBooks invoice was successfully updated."
    destroy:
      successfully_destroyed: "QuickBooks invoice was successfully destroyed."
    post_invoice:
      successfully_posted: "Draft Invoice successfully sent to QuickBooks."
      already_invoiced: "Unable to send to QuickBooks as this operation is already invoiced!"
    quickbooks_invoice:
      quickbooks_invoice_link: "View"
      draft_invoice: "Draft QuickBooks invoice"
      open_invoice: "Invoiced QuickBooks invoice"
      paid_invoice: "Paid QuickBooks invoice"
      qb_send_error: "Unable to send invoice, QuickBooks responded with the following: \n %{message}"
      qb_send_error_unrecoverable: "Unable to send invoice to QuickBooks"
      qb_send_success: "Successfully sent invoice to QuickBooks"
      qb_send_success_already_created: "Invoice is already available on QuickBooks"
      send_button_title: "Send by email"
      send_button_email_sent_at: "Email was already sent at %{time}"
      qb_email_send_success: "Email sent successfully"
      qb_email_send_failed: "Unable to send invoice to QuickBooks"
      receive_payment: "Receive payment"
      void_payment: "Cancel payment"
      send_payment_email: 'Send payment email'
      send_reminder_email: 'Send reminder'
      refund_payment: "Refund payment"
      select_payment_type: 'Select payment type'
      payment_email_not_send: 'Payment email has not been sent yet'
      payment_send_button_email_sent_at: "Payment email was already sent at %{time}"
    send_email:
      invalid_email: "Email %{bill_email} is invalid"
    send_email_modal:
      email_form_title: "Send invoice by email"
      email_label: "Email"
      send_button_title: "Send"
    send_payment_email:
      invalid_email: "Email %{payment_email} is invalid"
      email_triggered: "Email has been sent"
      email_send_failed: "Unable to send the email"
    send_payment_email_modal:
      email_form_title_default: "Send payment email"
      email_form_title_reminder: "Send reminder by email"
      email_label: "Email"
      send_button_title: "Send"
    download_pdf:
      failed: "Downloading invoice failed"
    pdf:
      download: "Download"
    receive_payment_modal:
      form_title: "Confirm"
      confirm_message: "Are you sure you want to mark as paid by %{payment_method}?"
      confirm_fl_pay_message: "Are you sure you want to charge %{payee} %{price} through FlightLogger Payment?"
      cancel: "Cancel"
      ok: "OK"
    cancel_payment_modal:
      form_title: "Confirm"
      confirm_message: "Are you sure you want to mark as unpaid?"
      cancel: "Cancel"
      ok: "OK"
      confirm_message_unpaid: "Are you sure you want to cancel FlightLogger Payment?"
      confirm_message_paid: "Payment was successfully processed on %{DATE} at %{TIME}. Are you sure you want to refund this invoice?"
    receive_payment:
      failed: "Unable to mark as paid"
      cancel: "Cancelled."
      payment_created_without_valid_email: Payment has been created for payee without a valid email
      payment_already_paid: Payment has already been received for this invoice
      payment_exist: "Payment already exists, syncing invoices"
    cancel_payment:
      failed: "Unable to mark as unpaid"
    payment:
      statuses:
        paid: "PAID"
        awaiting_approval: "AWAITING FL PAY"
        unpaid: "UNPAID"
        due: "DUE"
        overdue: "OVERDUE"
        refunded: "REFUNDED"
        declined: "DECLINED"
        processing: "PROCESSING"
        charged_back: "CHARGED BACK"
        unknown: ""
      refund_failed: "Refund failed"
    create_manual_draft:
      button: "Create manual draft"
      failed_to_create_manual: "Failed to create manual draft"
    manual_draft_modal:
      fl_user: FlightLogger User
      qb_customer: QuickBooks Customer
      title: "Create manual draft"
      save: "Create"
    refunded: "Refunded"
    refund_actions: "Actions"
  user_accountings:
    invoice_table:
      invoiced_at: "Invoiced at"
      status: "Status"
      registration: "Kind of Operation"
      total: "Total"
      no_record: "There are no entries in the given time period"
      view_link: "View"
    new_balance_button:
      new_transaction: New transaction
      receive_payment: Receive payment
    new_balance:
      new_transaction: New transaction
      comment: Comment
      create_tx: Create transaction
      receive_payment: Receive payment
      activity_date: Date of activity
      price: Price
      type: Transaction type
      freetext_label: Freetext
      positive_price: (+) Positive price will decrease the balance
      negative_price: (-) Negative price will increase the balance
      new_balance_title: New transaction
      new_balance_fl_pay_title: New payment request
    receive_payment_confirmation:
      title: Confirm
      confirm_fl_pay_message: "Are you sure you want to charge %{payee} %{price} through FlightLogger Payment?"
      ok: OK
      cancel: Cancel
    settings_enabled:
      discount: Discount number
      show_accounting: Below you can choose if user can see their own accounting information.
      show_own_accounting: See own balance
      show_own_invoices: See own invoices
    settings_disabled:
      header_title: User Balance Module (UBM) & QuickBooks Integration Module (QIM)
      customer_header_title: QuickBooks Integration Module (QIM)
      module_not_active_description: |
        The above modules UBM & QIM have not been activated on your FlightLogger platform.

        To have a free demo of one of these modules and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the UBM & QIM modules&body=I would like to know more about the UBM & QIM modules to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      customer_module_not_active_description: |
        The above module QIM has not been activated on your FlightLogger platform.

        To have a free demo of this module and possibly activate it on your account, please contact FlightLogger by email to <a href="mailto:<EMAIL>?subject=We are interested in knowing more about the QIM module&body=I would like to know more about the QIM module to possibly activate it on our account. Please reach out to me as soon as possible. %0D%0A %0D%0A Your name:%0D%0A Your email: %0D%0A Your phone number: %0D%0A Your organisation:"><EMAIL></a> or via the support widget in the top navigation bar.
      video_section:
        ubm_title: User Balance Module (UBM)
        qim_title: QuickBook Integration Module (QIM)
    payments:
      pay: Pay
      cancel: Cancel
      refund: Refund
      own_payments: You are not able to refund your own payments.
      send_reminder: Send reminder
      date: Date
      status: Status
      label: Item
      amount: Amount
      actions: Actions
      statuses:
        paid: Paid
        unpaid: Unpaid
        processing: Processing
        refunded: Refunded
        charged_back: Charged Back
        unknown: Unknown
      no_payments: There was no payments found in the given timespan
      created_at: Created at
      updated_at: Updated at
      payment_receipt: Download payment receipt
      refund_receipt: Download refund receipt
    card:
      automatic: Automatic payment
      set_automatic: Set automatic payment
      disable_automatic: Disable automatic payment
      enable: Enable
      disable: Disable
      remove: Remove
      pay: Pay
    cards:
      add_payment_method: Add a credit or debit card
      payment_methods: Payment methods
  user_accounting_report:
    report:
      invoiced_at: "Invoiced at"
      status: "Status"
      registration: "Kind of Operation"
      total: "Total"
      no_record: "There are no entries in the given time period"
  customer_report:
    report:
      quickbooks: "QuickBooks"
  quickbooks:
    invoice:
      state:
        create_draft: "Create Draft"
        show_draft: "Show Draft"
        invoice: "Invoiced"
  members:
    invite:
      success: "Invitation confirmed successfully"
      error: "Invitation could not be confirmed"
    profile:
      two_factor_authentication:
        mobile: "Use Authenticator App"
        email: "Use Email"
        mobile_description: "Make sure to scan the QR code below with your authentication app before activating 2-Factor Authentication."
        email_description: "The email (%{email}) will be used to send authentication codes. Make sure the email address is valid before activating 2-Factor Authentication."
        resend: "Send code via email"
  oauth:
    quickbooks_auth:
      index:
        access_denied: "Access denied"
  collaborations:
    groups: "Groups"
    students: "Students"
    index:
      title: "Collaborations"
      pending: "Pending"
      accepted: "Accepted"
      active: "Active"
      rejected: "Rejected"
      organization: "Organization"
      requested: "Requested"
      requester: "Requester"
      approver: "Approver"
      approved: "Approved"
      awaiting_approval: "Awaiting approval"
      program: "Program"
      currently_sharing: "Currently sharing"
      share: "Share"
      groups: "Groups"
      programs: "Programs"
      students: "Students"
      reject_collaboration: "Reject collaboration"
      accept_collaboration: "Approve collaboration"
      no_pending_collaborations: "There are no pending collaborations yet"
      no_active_collaborations: "There are no active collaborations yet"
      no_rejected_collaborations: "There are no rejected collaborations yet"
      are_you_sure_you_want_to_reject: "Are you sure you want to reject this collaboration?\nThis cannot be undone."
    collaborations_table_body:
      confirm_disable: "Are you sure you want to reject this collaboration?"
    tab_content:
      no_collaborations: "There is no collaborations present at this moment."
      organization_name: "Organization name"
      initiated_by: "Initiated by"
      initiated_at: "Initiated at"
      accept: "Accept"
      reject: "Reject"
      remove: "Remove"
      accepted_by: "Accepted by"
      rejected_by: "Rejected by"
      accepted_at: "Accepted at"
      rejected_at: "Rejected at"
    collaboration_requested_successfully: "Collaboration requested successfully"
    collaboration_accepted_successfully: "Collaboration accepted successfully"
    collaboration_rejected_successfully: "Collaboration rejected successfully"
    failed_to_request_collaboration: "Failed to request collaboration"
    failed_to_accept_collaboration: "Failed to approve collaboration"
    failed_to_reject_collaboration: "Failed to reject collaboration"
    list_of_shared_students_and_programs_updated_successfully: "List of shared students and programs updated successfully"
    failed_to_update_the_list_of_shared_students_and_programs: "Failed to update the list of shared students and programs"
    new:
      request: "Request "
      request_collaboration_header1: "By requesting a collaboration with another FlightLogger organization, you will (after approval) be able to see/share training programs that are attached to students who exist within both organization's FlightLogger accounts."
      request_collaboration_header2: "When sharing a training program, only the program overview is visible for the other organization. The content of each individual lesson, including briefing, grading, flight and debriefing information, is not visible."
      choose_the_flight_logger_organisation: "Choose the FlightLogger organization you want to start a collaboration with"
      subject: "Subject"
      message: "Message"
      send_request: "Send request"
      flight_logger_organization: "FlightLogger organization"
      organization_must_be_specified: "Organization must be specified"
    currently_shared_list:
      nothing_is_shared_yet: "Nothing is shared yet"
    create:
      organization_must_be_specified: "Organization must be specified"
  shared_programs:
    shared_programs_table:
      deactivate: "Are you sure?"
      ext_company: "EXT Company"
      program_name: "Program Name"
      active_from_to: "Active From - To"
  todo_items:
    todo_item:
      instructor: 'INSTRUCTOR'
  subgrades:
    subgrade:
      instructor: 'INSTRUCTOR'
  all_students: "All students"
  active_students: "Active students"
  standby_students: "Standby students"
  completed_students: "Completed students"
  discontinued_students: "Discontinued students"
  all_programs: "All programs"
  some_organization: "?"
  arg1_rejected_this_collaboration_arg2: "%{arg1} rejected this collaboration %{arg2}"
  back_to_collaborations_overview: "Back to collaborations overview"
  request: "Request"
  unknown_organization: "Unknown organization"
  an_error_occurred: "An error occurred"
  flight_logger_organization: "FlightLogger organization"
  programs_not_shared: "Program(s) not shared"
  plane_logs:
    form:
      add: "Add"
      primary_log: "Primary Log"
      secondary_log: "Secondary Log"
      tertiary_log: "Tertiary Log"
      special_log: "Special Logs"
    preview_log:
      preview: "Preview"
    flash_updated: "Flight logs have been updated"
    log_type_name: 'Name'
    log_type_measurement_name: 'Input'
    action_button_enabled: "Action buttons"
    prefill_label: "Preselect start to current"
    duration_warning_enabled: "Primary log deviation warning"
    offset_enabled: "Primary log offset (taxi)"
    offset_start: "Start offset (taxi out)"
    offset_end: "End offset (taxi in)"
    duration_deviation: "Maximum deviation"
    edit:
      save: "Update flight logs"
    instrument_enabled: "Instrument time"
    pmf_enabled: "Pilot flying/monitoring"
    asymmetric_enabled: "Asymmetric time"
    special_log_edit:
      default_pmf: "Default"
    log_type_duration_name: "Duration"
    must_be_within_primary: "Must be within primary log"
  components:
    concepts:
      theory_lectures_user:
        total_attendance: "Total attendance:"
        progress_bar_title: "%{percentage}% complete - %{completed} / %{total} hours"
      team_theory:
        category: Subject
        no_data: No data available for this category
      program_navigation:
        flight_training: Flight Training
        ground_training: Ground Training
  authentication:
    max_attempts_expiry: "Exceeded attempts, wait for %{duration}"
  member_programs:
    program_subject:
      date: "Date"
      theory_type: "Type"
      lesson: "Lesson/Note"
      student_comment: "student comment"
      instructor_abbreviated: "Inst."
      attendance: "Attendance / Duration"
    previous_theory_experience:
      credited_hours: "Credited hours"
  theory:
    links:
      instructor:
        blank_instructor: INST.
  widget_dashboard:
    your_overview: Your overview
    graphs_for_previous: Graphs for previous
    reset: Clear all
    apply: Apply
    add: Add
    edit: Edit
    menu: Menu
    no_user_widgets: No user widgets available
    all_widgets_in_use: All widgets in use
    manage_widgets: Manage widgets
    custom_date_range: "(%{start_date} - %{end_date})"
    selected_count: "%{count} selected"
    delete: Remove
    show_if_empty: Always show widget
    hide_if_empty: Hide widget if empty
    expandable: Expand widget if content allows it
    same_size: Always use standard widget size
    custom_date_range_selector_modal:
      title: Select custom date range
      save: Save
      cancel: Cancel
    presenter:
      time_intervals:
        today: Today
        yesterday: Yesterday
        last_week: Last week
        week_to_date: Week to date
        last_month: Last month
        month_to_date: Month to date
        last_quarter: Last quarter
        quarter_to_date: Quarter to date
        last_7_days: Last 7 days
        last_14_days: Last 14 days
        last_30_days: Last 30 days
        last_45_days: Last 45 days
        last_60_days: Last 60 days
        last_90_days: Last 90 days
        custom_date_range: Custom range
    widget_wrapper:
      view_more: View more
      view_less: View less
    update:
      error: Could not update the dashboard, try again later
    insights: FlightLogger Business Insights
    insights_free_trial: "FlightLogger Business Insights <span class='trial-until'>Free trial until %{trial_end_date}</span>"
    required_widgets: Required widgets
    update_widgets:
      category_title:
        safety_reports: Safety Management System (SMS)
        cbta_matrix_reports: Competency-Based Training and Assessment (CBTA-Pro)
    demo_widget_wrapper:
      disabled_show_hover_title:
        safety_reports_module_disabled: |-
          Safety Management System (SMS) is not enabled.

          To have a free demo of the Safety Management System (SMS) and possibly enable it within your FlightLogger organization, please contact FlightLogger by email (<EMAIL>) or via the support widget in the top navigation bar.
        safety_reports_not_safety_manager: You must be a Safety Manager or Deputy Safety Manager to access the Safety Management System (SMS) widgets
        cbta_reports_module_disabled: |-
          Competency-Based Training and Assessment (CBTA-Pro) is not enabled.

          To have a free demo of the Competency-Based Training and Assessment (CBTA-Pro) and possibly enable it within your FlightLogger organization, please contact FlightLogger by email (<EMAIL>) or via the support widget in the top navigation bar.
        cbta_reports_not_student: You must be a Student to access this Competency-Based Training and Assessment (CBTA-Pro) widget
  default_widgets:
    titles:
      competency_performance_full: Competency performance
      competency_performance_individual: Competency performance (%{call_sign})
      flight_phase_performance_full: Flight phase performance
      flight_phase_performance_individual: Flight phase performance (%{call_sign})
      performance_tracker_full: Performance tracker
      performance_tracker_individual: Performance tracker (%{call_sign})
      flight_training_progress: Flight training progression
      ground_training_progress: Ground training progression
      flight_phase_radar_full: Flight phase radar
      flight_phase_radar_individual: Flight phase radar (%{call_sign})
      simulator_hours_by_pic: Simulator hours by PIC
      aircraft_hours_by_pic: Aircraft hours by PIC
      weather: METAR & TAF
      maintenance_occupation: Maintenance occupation
      renter_productivity: Renter productivity
      crew_productivity: Crew productivity
      student_ground_productivity: Student ground productivity
      student_flight_productivity: Student flight productivity
      ground_instructor_productivity: Ground instructor productivity
      flight_instructor_productivity: Flight instructor productivity
      active_students: Active students
      aircraft_productivity: Aircraft productivity
      unapproved_lectures: Approval required
      bookings_overview: Bookings
      wall_posts: Wall posts
      certificate_warnings: Certificate warnings
      flight_time_warnings: Flight & duty time warnings
      ground_school_warnings: Ground training warnings
      aircraft_status: Aircraft status
      flight_hours_day: Flight hours by class
      flight_hours_aircraft: Flight hours by type
      user_flight_hours_day: Flight hours by class (%{scoped_by})
      user_flight_hours_aircraft: Flight hours by type (%{scoped_by})
      cancelled_flights_day: Cancelled flights by reason
      cancelled_ground_training: Cancelled ground training by reason
      scheduling_operation: Scheduling (operation)
      scheduling_rental: Scheduling (rental)
      scheduling_ground_training: Scheduling (ground training)
      scheduling_flight_training: Scheduling (flight training)
      scheduling_all_bookings: Scheduling (all bookings)
      scheduling_operation_graph: Scheduling (operation)
      scheduling_rental_graph: Scheduling (rental)
      scheduling_ground_training_graph: Scheduling (ground training)
      scheduling_flight_training_graph: Scheduling (flight training)
      scheduling_all_bookings_graph: Scheduling (all bookings)
      production_report_call_sign: Production report by call sign
      production_report_activities: Production report by activities
      production_report_aircraft_class: Production report by class
      production_report_aircraft_type: Production report by type
      flight_training_overview: Flight training overview
      safety_report_count: Safety Reports by date
      safety_report_user: Safety Reports by user
      safety_report_headline: Safety Reports by headline
      flight_training_exercise_pass_repetition: Exercises passed vs repetitions needed
      flight_training_passed_repetition: Lessons passed vs repetitions needed
      flight_training_students_instructors: Students trained vs flight instructors needed
      cbta_matrix_full: CBTA matrix
      cbta_matrix_individual: CBTA matrix (%{call_sign})
  user_widgets:
    presenter:
      updated_at: "Updated at %{time}"
      custom_date_range_time_interval_name: Custom range
    create:
      error: Widget could not be created
    update:
      error: Widget could not be updated
    destroy:
      success: Widget is removed from the dashboard
      error: Widget could not be deleted
    update_position:
      error: Widget position could not be updated
  widgets:
    no_permission: You no longer have permission to view this widget
    no_data: There are no %{widget_header} yet
    no_aircraft_status: No aircraft warnings - keep up the good work!
    trial_finished_admin: FlightLogger Business Insights is not activated on your account. To enable this feature and unlock the full range of widgets, please <a href="mailto:<EMAIL>">contact FlightLogger support</a>.
    trial_finished: FlightLogger Business Insights is not activated on your account. To enable this feature and unlock the full range of widgets, please contact your admins.
    status:
      activated: Activated
      not_activated: Not Activated
      trial: Trial
      trial_finished: Trial Finished
      paid: Paid
      trial_title: Trial is active until %{trial_end_date}
      paid_title: This is a paid widget
      trial_finished_title: Trial has been completed at %{trial_end_date}
      activated_title: FlightLogger Business Insights Module (BIM) has been activated
      not_activated_title: FlightLogger Business Insights Module (BIM) has not been activated
    cbta_matrix:
      cbta_matrix_component:
        dummy_label: CBTA enabled program
        cbta_enabled_entity_dummy_label: CBTA entities
        all_cbta_programs: All CBTA programs
        all_active_students: All active students
        students: Students
        classes: Classes
        cbta_programs: CBTA programs
    wall_posts:
      wall_posts_component:
        confirm_delete_wall_post: "Please note that you are about to delete a wall post for all users.\n\nAre you sure you want to delete the wall post?\nThis can not be undone!"
        create_new_message: Create
        messages: Wall posts
        messages_empty: There are no wall posts yet
        pin: Pin wall post
        unpin: Unpin wall post
        edit: Edit wall post
        delete: Delete wall post
    unapproved_lectures:
      unapproved_lectures_component:
        name: Name
        see_unapproved_lecture: See lesson and approve
        unapproved_lectures: Approval required
    scheduling:
      bookings_created: Bookings created
      flights_registered: Flights registered
      bookings_completed: Bookings completed
      completion: Completion
      bookings_missing_completion: Bookings missing completion
      unknown: Unknown
      bookings_cancelled: Bookings cancelled
      cancellation: Cancellation
      ground_registered: Ground registered
      hours_registered: Hours registered
      conversion: Conversion
      avg_duration: Average duration
    weather:
      no_data_available: No data available
      last_updated_with_time: "Last updated at %{time}"
    flight_training_overview:
      students_trained: Students trained
      lesson_attempts: Lesson attempts
      exercises_graded: Exercises graded
      average_grading: Average grading
      lessons_passed: Lessons passed
      exercises_passed: Exercises passed
      course_enrollments: Course enrollments
      lessons_repetition_needed: Repetition needed
      exercises_repetition_needed: Repetition needed
      course_completions: Course completion
      lessons_pass_rate: Pass rate
      exercises_pass_rate: Pass rate
    flight_training_students_instructors:
      types:
        instructors: Instructors
        students: Students
    training_progress:
      classes: Classes
      active_students: Students
      all_active_students: All active students
      programs: Programs
      all_programs: All active programs
      last_updated_with_time: "Last updated at %{time}"
      students: "%{team_name}"
      choose_program: Choose a program
      choose_class: Choose a class
      no_active_students: "There is no active %{training_type} training students within this selection"
    cbta_performance:
      no_data: There are no CBTA data yet
    flight_phase_radar:
      no_data: There are no CBTA data yet
  theory_registration:
    flash_completed_booking: "The chosen booking has already been completed and this registration can thus not also be completed. Leave this page open and delete the other registration in order to save this registration"
  external_payment:
    show:
      redirecting_please_wait: Redirecting... please wait
      payment_already_paid: Payment has already been paid, you can close this page.
      payment_no_longer_exists: This payment no longer exists, you can close this page.
  access_permissions:
    show:
      title_user: Permissions for %{permissible_title}
      title_group: Permissions for %{permissible_title}
      title_role: Permissions for %{permissible_title}
      subtitle_user: Personal permissions
      subtitle_group: Group permissions
      subtitle_role: Role permissions
      user_help_text: A user’s total set of permissions is the sum of the roles given, but additional personal permissions can be added on this page
    tabs:
      aircraft: Aircraft
      departure_arrival: Departures/Arrivals
      ubm: User Balance Module (UBM)
      certificate: Certificates
      student: Students
      report: Reports
      operation: Operations
      booking: Bookings
      rental: Rentals
      program: Programs
      user: Users
    flash:
      update_succeeded: Permission was successfully updated
      update_failed: Permission could not be updated
      not_found: "Resource not found"
    options:
      all: All
      own: Own
      other: Other
      read: View
      view: View
      none: None
      students: Students
      instructors: Instructors
      manage: Manage
    access_block:
      aircraft_access_title: Aircraft access
      aircraft_access_description: View or manage aircraft and maintenance
      booking_access_title: Booking access
      booking_access_description: View or manage bookings
      ubm_access_title: User Balance Module access
      ubm_access_description: Access to view or manage User Balance Module
      program_access_title: Program access
      program_access_description: Program management
      certificate_access_title: Certificates access
      certificate_access_description: Read, manage or approve own, others or all certificates
      departure_arrival_access_title: Departures/Arrivals access
      departure_arrival_access_description: View or manage departures/arrivals
      student_access_title: Student access
      student_access_description: Student profiles, classes and details
      rental_access_title: Rentals access
      rental_access_description: View or manage rentals
      rental_own_reg_permission: Which actions are available for own rental registrations?
      rental_other_reg_permission: Which actions are available for other rental registrations?
      report_access_title: Reports access
      report_access_description: View reports in FlightLogger
      student_flight_program_access_title: Flight training
      student_flight_program_access_description: Access to students flight training programs
      student_ground_program_access_title: Ground training
      student_ground_program_access_description: Access to students ground training programs
      operation_access_title: Operations access
      operation_access_description: View or manage operations
    permission_domains:
      programs:
        title: Configure permissions for program management
      users:
        title: Configure permissions for user management
  squawks:
    actionable_entity: discrepancy
    section_title: Discrepancies
    report_new: Report discrepancy
    preflight_inspection: Preflight inspection
    requiring_approval: Requiring approval
    current: Current discrepancies
    previous: Resolved discrepancies
    rejected: Rejected discrepancies
    approve_action: Approve
    resolve_action: Resolve
    confirm_delete: Are you sure you want to delete this discrepancy?
    no_squawks: No discrepancies found for this aircraft.
    title: Title
    description: Description
    issue_description: Issue description
    resolution_title: Resolution title
    reporter: Reported by
    created_by: Created by
    resolved_by: Resolved by
    date: Date
    resolved_date: Resolved date
    rejection_title: Rejection title
    rejected_by: Rejected by
    approved_by: Approved by
    reject_action: Reject
    severity: Severity
    actions: Actions
    new:
      title: Report discrepancy on %{call_sign}
    edit:
      title: Edit discrepancy on %{call_sign}
    show:
      title: Discrepancy %{title} on %{call_sign}
    resolve:
      title: Resolve %{title} on %{call_sign}
      disabled_title: Resolution %{title} on %{call_sign}
    reject:
      title: Reject %{title} on %{call_sign}
      disabled_title: Rejection %{title} on %{call_sign}
    form:
      save: Create discrepancy
      aircraft: Aircraft
      location: Location
      select_aircraft: Select aircraft...
      select_location: Select location...
      select_severity: Select severity...
      date: Date
      description_title: Issue title
      description: Description
      severity: Severity
    resolve_form:
      resolution_title: Resolution title
      resolution_reference: Resolution reference
      resolution_details: Resolution details
    reject_form:
      rejection_title: Rejection title
    flash:
      created: Discrepancy successfully created
      create_failed: Creation of discrepancy failed
      updated: Discrepancy successfully updated
      update_failed: Update of discrepancy failed
      approved: Discrepancy successfully approved
      approve_failed: Approval of discrepancy failed
      resolved: Discrepancy successfully resolved
      resolve_failed: Resolving of discrepancy failed
      rejected: Discrepancy successfully rejected
      reject_failed: Rejection of discrepancy failed
